<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [QA_G0\QA_G0.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image QA_G0\QA_G0.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6230001: Last Updated: Fri Aug 15 16:03:25 2025
<BR><P>
<H3>Maximum Stack Usage =        304 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
__rt_entry_main &rArr; main &rArr; MX_USB_Device_Init &rArr; USBD_Init &rArr; USBD_LL_Init &rArr; HAL_PCD_Init &rArr; HAL_PCD_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[52]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[2a]">ADC1_COMP_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2a]">ADC1_COMP_IRQHandler</a><BR>
 <LI><a href="#[1a]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1a]">HardFault_Handler</a><BR>
 <LI><a href="#[19]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[19]">NMI_Handler</a><BR>
 <LI><a href="#[99]">my_main</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[99]">my_main</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[2a]">ADC1_COMP_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[3c]">CEC_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[16]">CUSTOM_HID_DeInit_FS</a> from usbd_custom_hid_if.o(.text.CUSTOM_HID_DeInit_FS) referenced 2 times from usbd_custom_hid_if.o(.data.USBD_CustomHID_fops_FS)
 <LI><a href="#[15]">CUSTOM_HID_Init_FS</a> from usbd_custom_hid_if.o(.text.CUSTOM_HID_Init_FS) referenced 2 times from usbd_custom_hid_if.o(.data.USBD_CustomHID_fops_FS)
 <LI><a href="#[17]">CUSTOM_HID_OutEvent_FS</a> from usbd_custom_hid_if.o(.text.CUSTOM_HID_OutEvent_FS) referenced 2 times from usbd_custom_hid_if.o(.data.USBD_CustomHID_fops_FS)
 <LI><a href="#[29]">DMA1_Ch4_7_DMA2_Ch1_5_DMAMUX1_OVR_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[27]">DMA1_Channel1_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[28]">DMA1_Channel2_3_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[23]">EXTI0_1_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[24]">EXTI2_3_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[25]">EXTI4_15_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[21]">FLASH_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[1a]">HardFault_Handler</a> from stm32g0xx_it.o(.text.HardFault_Handler) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[35]">I2C1_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[36]">I2C2_3_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[19]">NMI_Handler</a> from stm32g0xx_it.o(.text.NMI_Handler) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[1f]">PVD_VDDIO2_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[1c]">PendSV_Handler</a> from stm32g0xx_it.o(.text.PendSV_Handler) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[22]">RCC_CRS_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[20]">RTC_TAMP_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[18]">Reset_Handler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[37]">SPI1_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[38]">SPI2_3_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[1b]">SVC_Handler</a> from stm32g0xx_it.o(.text.SVC_Handler) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[1d]">SysTick_Handler</a> from stm32g0xx_it.o(.text.SysTick_Handler) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[3d]">SystemInit</a> from system_stm32g0xx.o(.text.SystemInit) referenced from startup_stm32g0b1xx.o(.text)
 <LI><a href="#[31]">TIM14_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[32]">TIM15_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[33]">TIM16_FDCAN_IT0_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[34]">TIM17_FDCAN_IT1_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[2b]">TIM1_BRK_UP_TRG_COM_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[2c]">TIM1_CC_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[2d]">TIM2_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[2e]">TIM3_TIM4_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[2f]">TIM6_DAC_LPTIM1_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[30]">TIM7_LPTIM2_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[39]">USART1_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[3a]">USART2_LPUART2_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[3b]">USART3_4_5_6_LPUART1_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[9]">USBD_CUSTOM_HID_ConfigStrDescriptor</a> from usbd_desc.o(.text.USBD_CUSTOM_HID_ConfigStrDescriptor) referenced 2 times from usbd_desc.o(.data.CUSTOM_HID_Desc)
 <LI><a href="#[f]">USBD_CUSTOM_HID_DataIn</a> from usbd_customhid.o(.text.USBD_CUSTOM_HID_DataIn) referenced 2 times from usbd_customhid.o(.data.USBD_CUSTOM_HID)
 <LI><a href="#[10]">USBD_CUSTOM_HID_DataOut</a> from usbd_customhid.o(.text.USBD_CUSTOM_HID_DataOut) referenced 2 times from usbd_customhid.o(.data.USBD_CUSTOM_HID)
 <LI><a href="#[c]">USBD_CUSTOM_HID_DeInit</a> from usbd_customhid.o(.text.USBD_CUSTOM_HID_DeInit) referenced 2 times from usbd_customhid.o(.data.USBD_CUSTOM_HID)
 <LI><a href="#[4]">USBD_CUSTOM_HID_DeviceDescriptor</a> from usbd_desc.o(.text.USBD_CUSTOM_HID_DeviceDescriptor) referenced 2 times from usbd_desc.o(.data.CUSTOM_HID_Desc)
 <LI><a href="#[e]">USBD_CUSTOM_HID_EP0_RxReady</a> from usbd_customhid.o(.text.USBD_CUSTOM_HID_EP0_RxReady) referenced 2 times from usbd_customhid.o(.data.USBD_CUSTOM_HID)
 <LI><a href="#[14]">USBD_CUSTOM_HID_GetDeviceQualifierDesc</a> from usbd_customhid.o(.text.USBD_CUSTOM_HID_GetDeviceQualifierDesc) referenced 2 times from usbd_customhid.o(.data.USBD_CUSTOM_HID)
 <LI><a href="#[12]">USBD_CUSTOM_HID_GetFSCfgDesc</a> from usbd_customhid.o(.text.USBD_CUSTOM_HID_GetFSCfgDesc) referenced 2 times from usbd_customhid.o(.data.USBD_CUSTOM_HID)
 <LI><a href="#[11]">USBD_CUSTOM_HID_GetHSCfgDesc</a> from usbd_customhid.o(.text.USBD_CUSTOM_HID_GetHSCfgDesc) referenced 2 times from usbd_customhid.o(.data.USBD_CUSTOM_HID)
 <LI><a href="#[13]">USBD_CUSTOM_HID_GetOtherSpeedCfgDesc</a> from usbd_customhid.o(.text.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc) referenced 2 times from usbd_customhid.o(.data.USBD_CUSTOM_HID)
 <LI><a href="#[b]">USBD_CUSTOM_HID_Init</a> from usbd_customhid.o(.text.USBD_CUSTOM_HID_Init) referenced 2 times from usbd_customhid.o(.data.USBD_CUSTOM_HID)
 <LI><a href="#[a]">USBD_CUSTOM_HID_InterfaceStrDescriptor</a> from usbd_desc.o(.text.USBD_CUSTOM_HID_InterfaceStrDescriptor) referenced 2 times from usbd_desc.o(.data.CUSTOM_HID_Desc)
 <LI><a href="#[5]">USBD_CUSTOM_HID_LangIDStrDescriptor</a> from usbd_desc.o(.text.USBD_CUSTOM_HID_LangIDStrDescriptor) referenced 2 times from usbd_desc.o(.data.CUSTOM_HID_Desc)
 <LI><a href="#[6]">USBD_CUSTOM_HID_ManufacturerStrDescriptor</a> from usbd_desc.o(.text.USBD_CUSTOM_HID_ManufacturerStrDescriptor) referenced 2 times from usbd_desc.o(.data.CUSTOM_HID_Desc)
 <LI><a href="#[7]">USBD_CUSTOM_HID_ProductStrDescriptor</a> from usbd_desc.o(.text.USBD_CUSTOM_HID_ProductStrDescriptor) referenced 2 times from usbd_desc.o(.data.CUSTOM_HID_Desc)
 <LI><a href="#[8]">USBD_CUSTOM_HID_SerialStrDescriptor</a> from usbd_desc.o(.text.USBD_CUSTOM_HID_SerialStrDescriptor) referenced 2 times from usbd_desc.o(.data.CUSTOM_HID_Desc)
 <LI><a href="#[d]">USBD_CUSTOM_HID_Setup</a> from usbd_customhid.o(.text.USBD_CUSTOM_HID_Setup) referenced 2 times from usbd_customhid.o(.data.USBD_CUSTOM_HID)
 <LI><a href="#[26]">USB_UCPD1_2_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[1e]">WWDG_IRQHandler</a> from startup_stm32g0b1xx.o(.text) referenced from startup_stm32g0b1xx.o(RESET)
 <LI><a href="#[3e]">__main</a> from __main.o(!!!main) referenced from startup_stm32g0b1xx.o(.text)
 <LI><a href="#[3]">after_scan_key</a> from scan_key.o(.text.after_scan_key) referenced 2 times from scan_key.o(.data..L_MergedGlobals)
 <LI><a href="#[2]">before_scan_key</a> from scan_key.o(.text.before_scan_key) referenced 2 times from scan_key.o(.data..L_MergedGlobals)
 <LI><a href="#[0]">key_flash_event</a> from scan_key.o(.text.key_flash_event) referenced 2 times from scan_key.o(.data..L_MergedGlobals)
 <LI><a href="#[1]">key_mode_event</a> from scan_key.o(.text.key_mode_event) referenced 2 times from scan_key.o(.data..L_MergedGlobals)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[3e]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(.text)
</UL>
<P><STRONG><a name="[3f]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[41]"></a>__scatterload_rt2</STRONG> (Thumb, 74 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[9c]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[9d]"></a>__scatterload_loop</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[9e]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, __scatter.o(!!handler_null), UNUSED)

<P><STRONG><a name="[9f]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[45]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[a0]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[a1]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[a2]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[a3]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[a4]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000034))

<P><STRONG><a name="[a5]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[a6]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000002))

<P><STRONG><a name="[a7]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[a8]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[a9]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[aa]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[ab]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[ac]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[ad]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[ae]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[af]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000006))

<P><STRONG><a name="[b0]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000010))

<P><STRONG><a name="[b1]"></a>__rt_lib_init_relocate_pie_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[b2]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000035))

<P><STRONG><a name="[b3]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[b4]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000027))

<P><STRONG><a name="[b5]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[4a]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[b6]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[b7]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000007))

<P><STRONG><a name="[b8]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[b9]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000010))

<P><STRONG><a name="[ba]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A))

<P><STRONG><a name="[bb]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[bc]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[40]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[bd]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[42]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[44]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[be]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[46]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 304 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; MX_USB_Device_Init &rArr; USBD_Init &rArr; USBD_LL_Init &rArr; HAL_PCD_Init &rArr; HAL_PCD_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[bf]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[54]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[49]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[c0]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[4b]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[18]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>ADC1_COMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_COMP_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_COMP_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>CEC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>DMA1_Ch4_7_DMA2_Ch1_5_DMAMUX1_OVR_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>DMA1_Channel2_3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>EXTI0_1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>EXTI2_3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>EXTI4_15_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>I2C1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>I2C2_3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>PVD_VDDIO2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>RCC_CRS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>RTC_TAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>SPI2_3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>TIM15_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>TIM16_FDCAN_IT0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>TIM17_FDCAN_IT1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIM1_BRK_UP_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>TIM3_TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>TIM6_DAC_LPTIM1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>TIM7_LPTIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>USART2_LPUART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>USART3_4_5_6_LPUART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>USB_UCPD1_2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g0b1xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_stm32g0b1xx.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[4e]"></a>_memset_w</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memclr_w
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset
</UL>

<P><STRONG><a name="[4d]"></a>_memset</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset_w
</UL>
<BR>[Called By]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[4f]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset
</UL>

<P><STRONG><a name="[c1]"></a>__rt_memclr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)

<P><STRONG><a name="[6b]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memclr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_MspInit
</UL>

<P><STRONG><a name="[c2]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)

<P><STRONG><a name="[50]"></a>__rt_memclr_w</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset_w
</UL>

<P><STRONG><a name="[c3]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[c4]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[c5]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[43]"></a>__user_setup_stackheap</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[48]"></a>exit</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_call_atexit_fns (Weak Reference)
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[c6]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[51]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[c7]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[4c]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[c8]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[c9]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[ca]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[cb]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[cc]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[6d]"></a>Error_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, main.o(.text.Error_Handler))
<BR><BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Init
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_MspInit
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USB_Device_Init
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI3_Init
</UL>

<P><STRONG><a name="[78]"></a>HAL_GPIO_Init</STRONG> (Thumb, 396 bytes, Stack size 60 bytes, stm32g0xx_hal_gpio.o(.text.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
</UL>

<P><STRONG><a name="[97]"></a>HAL_GPIO_ReadPin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32g0xx_hal_gpio.o(.text.HAL_GPIO_ReadPin))
<BR><BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_event_get
</UL>

<P><STRONG><a name="[98]"></a>HAL_GPIO_TogglePin</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32g0xx_hal_gpio.o(.text.HAL_GPIO_TogglePin))
<BR><BR>[Called By]<UL><LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_flash_event
</UL>

<P><STRONG><a name="[7d]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32g0xx_hal_gpio.o(.text.HAL_GPIO_WritePin))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
</UL>

<P><STRONG><a name="[73]"></a>HAL_GetTick</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32g0xx_hal.o(.text.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
</UL>

<P><STRONG><a name="[86]"></a>HAL_IncTick</STRONG> (Thumb, 88 bytes, Stack size 0 bytes, system.o(.text.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[56]"></a>HAL_Init</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, stm32g0xx_hal.o(.text.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_Init &rArr; HAL_InitTick
</UL>
<BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[57]"></a>HAL_InitTick</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, stm32g0xx_hal.o(.text.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_InitTick
</UL>
<BR>[Calls]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidiv
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[58]"></a>HAL_MspInit</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, stm32g0xx_hal_msp.o(.text.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_MspInit
</UL>
<BR>[Calls]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSCFG_StrobeDBattpinsConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[5b]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, stm32g0xx_hal_cortex.o(.text.HAL_NVIC_SetPriority))
<BR><BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[6a]"></a>HAL_PCDEx_ActivateLPM</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_ActivateLPM))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_PCDEx_ActivateLPM
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Init
</UL>

<P><STRONG><a name="[94]"></a>HAL_PCDEx_PMAConfig</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_PMAConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_PCDEx_PMAConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Init
</UL>

<P><STRONG><a name="[5d]"></a>HAL_PCD_EP_Close</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Close))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_PCD_EP_Close &rArr; USB_DeactivateEndpoint
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeactivateEndpoint
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_CloseEP
</UL>

<P><STRONG><a name="[5f]"></a>HAL_PCD_EP_Open</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Open))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_PCD_EP_Open &rArr; USB_ActivateEndpoint
</UL>
<BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_ActivateEndpoint
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_OpenEP
</UL>

<P><STRONG><a name="[61]"></a>HAL_PCD_EP_Receive</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = HAL_PCD_EP_Receive &rArr; USB_EPStartXfer
</UL>
<BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EPStartXfer
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_PrepareReceive
</UL>

<P><STRONG><a name="[63]"></a>HAL_PCD_EP_SetStall</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_SetStall))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_PCD_EP_SetStall &rArr; USB_EPSetStall
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EPSetStall
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_StallEP
</UL>

<P><STRONG><a name="[65]"></a>HAL_PCD_EP_Transmit</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer
</UL>
<BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EPStartXfer
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Transmit
</UL>

<P><STRONG><a name="[66]"></a>HAL_PCD_Init</STRONG> (Thumb, 580 bytes, Stack size 40 bytes, stm32g0xx_hal_pcd.o(.text.HAL_PCD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = HAL_PCD_Init &rArr; HAL_PCD_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCDEx_ActivateLPM
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DevInit
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DisableGlobalInt
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Init
</UL>

<P><STRONG><a name="[67]"></a>HAL_PCD_MspInit</STRONG> (Thumb, 112 bytes, Stack size 96 bytes, usbd_conf.o(.text.HAL_PCD_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = HAL_PCD_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWREx_EnableVddUSB
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Init
</UL>

<P><STRONG><a name="[6f]"></a>HAL_PCD_Start</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, stm32g0xx_hal_pcd.o(.text.HAL_PCD_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_PCD_Start
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DevConnect
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EnableGlobalInt
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Start
</UL>

<P><STRONG><a name="[72]"></a>HAL_PWREx_ControlVoltageScaling</STRONG> (Thumb, 120 bytes, Stack size 24 bytes, stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_PWREx_ControlVoltageScaling
</UL>
<BR>[Calls]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidiv
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6e]"></a>HAL_PWREx_EnableVddUSB</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableVddUSB))
<BR><BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_MspInit
</UL>

<P><STRONG><a name="[6c]"></a>HAL_RCCEx_PeriphCLKConfig</STRONG> (Thumb, 664 bytes, Stack size 48 bytes, stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_MspInit
</UL>

<P><STRONG><a name="[74]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 480 bytes, Stack size 32 bytes, stm32g0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_InitTick
</UL>
<BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidiv
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[75]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 1200 bytes, Stack size 48 bytes, stm32g0xx_hal_rcc.o(.text.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_RCC_OscConfig &rArr; HAL_InitTick
</UL>
<BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[76]"></a>HAL_SPI_Init</STRONG> (Thumb, 244 bytes, Stack size 40 bytes, stm32g0xx_hal_spi.o(.text.HAL_SPI_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI3_Init
</UL>

<P><STRONG><a name="[77]"></a>HAL_SPI_MspInit</STRONG> (Thumb, 132 bytes, Stack size 40 bytes, spi.o(.text.HAL_SPI_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
</UL>

<P><STRONG><a name="[5c]"></a>HAL_SYSCFG_StrobeDBattpinsConfig</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32g0xx_hal.o(.text.HAL_SYSCFG_StrobeDBattpinsConfig))
<BR><BR>[Called By]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
</UL>

<P><STRONG><a name="[5a]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, stm32g0xx_hal_cortex.o(.text.HAL_SYSTICK_Config))
<BR><BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[80]"></a>HAL_TIMEx_MasterConfigSynchronization</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_TIMEx_MasterConfigSynchronization
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
</UL>

<P><STRONG><a name="[79]"></a>HAL_TIM_Base_Init</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
</UL>

<P><STRONG><a name="[7a]"></a>HAL_TIM_Base_MspInit</STRONG> (Thumb, 36 bytes, Stack size 4 bytes, tim.o(.text.HAL_TIM_Base_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIM_Base_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[1a]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g0xx_it.o(.text.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[7c]"></a>MX_GPIO_Init</STRONG> (Thumb, 212 bytes, Stack size 56 bytes, gpio.o(.text.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = MX_GPIO_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7e]"></a>MX_SPI3_Init</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, spi.o(.text.MX_SPI3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = MX_SPI3_Init &rArr; HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7f]"></a>MX_TIM6_Init</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, tim.o(.text.MX_TIM6_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = MX_TIM6_Init &rArr; HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[81]"></a>MX_USB_Device_Init</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, usb_device.o(.text.MX_USB_Device_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = MX_USB_Device_Init &rArr; USBD_Init &rArr; USBD_LL_Init &rArr; HAL_PCD_Init &rArr; HAL_PCD_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Start
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_RegisterInterface
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_RegisterClass
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Init
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[19]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g0xx_it.o(.text.NMI_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g0xx_it.o(.text.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g0xx_it.o(.text.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>SysTick_Handler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32g0xx_it.o(.text.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTick_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>SystemInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, system_stm32g0xx.o(.text.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g0b1xx.o(.text)
</UL>
<P><STRONG><a name="[7b]"></a>TIM_Base_SetConfig</STRONG> (Thumb, 204 bytes, Stack size 8 bytes, stm32g0xx_hal_tim.o(.text.TIM_Base_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[9]"></a>USBD_CUSTOM_HID_ConfigStrDescriptor</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, usbd_desc.o(.text.USBD_CUSTOM_HID_ConfigStrDescriptor))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USBD_CUSTOM_HID_ConfigStrDescriptor &rArr; USBD_GetString
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetString
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_desc.o(.data.CUSTOM_HID_Desc)
</UL>
<P><STRONG><a name="[4]"></a>USBD_CUSTOM_HID_DeviceDescriptor</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_desc.o(.text.USBD_CUSTOM_HID_DeviceDescriptor))
<BR>[Address Reference Count : 1]<UL><LI> usbd_desc.o(.data.CUSTOM_HID_Desc)
</UL>
<P><STRONG><a name="[a]"></a>USBD_CUSTOM_HID_InterfaceStrDescriptor</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, usbd_desc.o(.text.USBD_CUSTOM_HID_InterfaceStrDescriptor))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USBD_CUSTOM_HID_InterfaceStrDescriptor &rArr; USBD_GetString
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetString
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_desc.o(.data.CUSTOM_HID_Desc)
</UL>
<P><STRONG><a name="[5]"></a>USBD_CUSTOM_HID_LangIDStrDescriptor</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_desc.o(.text.USBD_CUSTOM_HID_LangIDStrDescriptor))
<BR>[Address Reference Count : 1]<UL><LI> usbd_desc.o(.data.CUSTOM_HID_Desc)
</UL>
<P><STRONG><a name="[6]"></a>USBD_CUSTOM_HID_ManufacturerStrDescriptor</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, usbd_desc.o(.text.USBD_CUSTOM_HID_ManufacturerStrDescriptor))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USBD_CUSTOM_HID_ManufacturerStrDescriptor &rArr; USBD_GetString
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetString
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_desc.o(.data.CUSTOM_HID_Desc)
</UL>
<P><STRONG><a name="[7]"></a>USBD_CUSTOM_HID_ProductStrDescriptor</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, usbd_desc.o(.text.USBD_CUSTOM_HID_ProductStrDescriptor))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USBD_CUSTOM_HID_ProductStrDescriptor &rArr; USBD_GetString
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetString
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_desc.o(.data.CUSTOM_HID_Desc)
</UL>
<P><STRONG><a name="[55]"></a>USBD_CUSTOM_HID_ReceivePacket</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, usbd_customhid.o(.text.USBD_CUSTOM_HID_ReceivePacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = USBD_CUSTOM_HID_ReceivePacket &rArr; USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_PrepareReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CUSTOM_HID_OutEvent_FS
</UL>

<P><STRONG><a name="[84]"></a>USBD_CUSTOM_HID_RegisterInterface</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, usbd_customhid.o(.text.USBD_CUSTOM_HID_RegisterInterface))
<BR><BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USB_Device_Init
</UL>

<P><STRONG><a name="[8]"></a>USBD_CUSTOM_HID_SerialStrDescriptor</STRONG> (Thumb, 264 bytes, Stack size 8 bytes, usbd_desc.o(.text.USBD_CUSTOM_HID_SerialStrDescriptor))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USBD_CUSTOM_HID_SerialStrDescriptor
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_desc.o(.data.CUSTOM_HID_Desc)
</UL>
<P><STRONG><a name="[90]"></a>USBD_CtlError</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, usbd_ctlreq.o(.text.USBD_CtlError))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = USBD_CtlError &rArr; USBD_LL_StallEP &rArr; HAL_PCD_EP_SetStall &rArr; USB_EPSetStall
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_StallEP
</UL>
<BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_Setup
</UL>

<P><STRONG><a name="[8f]"></a>USBD_CtlPrepareRx</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, usbd_ioreq.o(.text.USBD_CtlPrepareRx))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = USBD_CtlPrepareRx &rArr; USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_PrepareReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_Setup
</UL>

<P><STRONG><a name="[8e]"></a>USBD_CtlSendData</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, usbd_ioreq.o(.text.USBD_CtlSendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = USBD_CtlSendData &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_Setup
</UL>

<P><STRONG><a name="[8a]"></a>USBD_GetEpDesc</STRONG> (Thumb, 122 bytes, Stack size 16 bytes, usbd_core.o(.text.USBD_GetEpDesc))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USBD_GetEpDesc
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_GetOtherSpeedCfgDesc
<LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_GetFSCfgDesc
<LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_GetHSCfgDesc
</UL>

<P><STRONG><a name="[87]"></a>USBD_GetString</STRONG> (Thumb, 172 bytes, Stack size 16 bytes, usbd_ctlreq.o(.text.USBD_GetString))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USBD_GetString
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_InterfaceStrDescriptor
<LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_ConfigStrDescriptor
<LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_ProductStrDescriptor
<LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_ManufacturerStrDescriptor
</UL>

<P><STRONG><a name="[82]"></a>USBD_Init</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, usbd_core.o(.text.USBD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = USBD_Init &rArr; USBD_LL_Init &rArr; HAL_PCD_Init &rArr; HAL_PCD_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USB_Device_Init
</UL>

<P><STRONG><a name="[88]"></a>USBD_LL_CloseEP</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, usbd_conf.o(.text.USBD_LL_CloseEP))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USBD_LL_CloseEP &rArr; HAL_PCD_EP_Close &rArr; USB_DeactivateEndpoint
</UL>
<BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Close
</UL>
<BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_DeInit
</UL>

<P><STRONG><a name="[93]"></a>USBD_LL_Init</STRONG> (Thumb, 112 bytes, Stack size 16 bytes, usbd_conf.o(.text.USBD_LL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = USBD_LL_Init &rArr; HAL_PCD_Init &rArr; HAL_PCD_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCDEx_PMAConfig
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Init
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Init
</UL>

<P><STRONG><a name="[8c]"></a>USBD_LL_OpenEP</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, usbd_conf.o(.text.USBD_LL_OpenEP))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = USBD_LL_OpenEP &rArr; HAL_PCD_EP_Open &rArr; USB_ActivateEndpoint
</UL>
<BR>[Calls]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Open
</UL>
<BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_Init
</UL>

<P><STRONG><a name="[8d]"></a>USBD_LL_PrepareReceive</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, usbd_conf.o(.text.USBD_LL_PrepareReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer
</UL>
<BR>[Calls]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Receive
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_ReceivePacket
<LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_Init
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlPrepareRx
</UL>

<P><STRONG><a name="[91]"></a>USBD_LL_StallEP</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, usbd_conf.o(.text.USBD_LL_StallEP))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USBD_LL_StallEP &rArr; HAL_PCD_EP_SetStall &rArr; USB_EPSetStall
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_SetStall
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlError
</UL>

<P><STRONG><a name="[95]"></a>USBD_LL_Start</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, usbd_conf.o(.text.USBD_LL_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USBD_LL_Start &rArr; HAL_PCD_Start
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Start
</UL>

<P><STRONG><a name="[92]"></a>USBD_LL_Transmit</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, usbd_conf.o(.text.USBD_LL_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer
</UL>
<BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendData
</UL>

<P><STRONG><a name="[83]"></a>USBD_RegisterClass</STRONG> (Thumb, 66 bytes, Stack size 24 bytes, usbd_core.o(.text.USBD_RegisterClass))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USBD_RegisterClass
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USB_Device_Init
</UL>

<P><STRONG><a name="[85]"></a>USBD_Start</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, usbd_core.o(.text.USBD_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USBD_Start &rArr; USBD_LL_Start &rArr; HAL_PCD_Start
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USB_Device_Init
</UL>

<P><STRONG><a name="[89]"></a>USBD_static_free</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, usbd_conf.o(.text.USBD_static_free))
<BR><BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_DeInit
</UL>

<P><STRONG><a name="[8b]"></a>USBD_static_malloc</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, usbd_conf.o(.text.USBD_static_malloc))
<BR><BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_Init
</UL>

<P><STRONG><a name="[60]"></a>USB_ActivateEndpoint</STRONG> (Thumb, 516 bytes, Stack size 32 bytes, stm32g0xx_ll_usb.o(.text.USB_ActivateEndpoint))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USB_ActivateEndpoint
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Open
</UL>

<P><STRONG><a name="[5e]"></a>USB_DeactivateEndpoint</STRONG> (Thumb, 164 bytes, Stack size 16 bytes, stm32g0xx_ll_usb.o(.text.USB_DeactivateEndpoint))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USB_DeactivateEndpoint
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Close
</UL>

<P><STRONG><a name="[71]"></a>USB_DevConnect</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32g0xx_ll_usb.o(.text.USB_DevConnect))
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Start
</UL>

<P><STRONG><a name="[69]"></a>USB_DevInit</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32g0xx_ll_usb.o(.text.USB_DevInit))
<BR><BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Init
</UL>

<P><STRONG><a name="[68]"></a>USB_DisableGlobalInt</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32g0xx_ll_usb.o(.text.USB_DisableGlobalInt))
<BR><BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Init
</UL>

<P><STRONG><a name="[64]"></a>USB_EPSetStall</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, stm32g0xx_ll_usb.o(.text.USB_EPSetStall))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USB_EPSetStall
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_SetStall
</UL>

<P><STRONG><a name="[62]"></a>USB_EPStartXfer</STRONG> (Thumb, 3104 bytes, Stack size 52 bytes, stm32g0xx_ll_usb.o(.text.USB_EPStartXfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = USB_EPStartXfer
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Receive
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Transmit
</UL>

<P><STRONG><a name="[70]"></a>USB_EnableGlobalInt</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32g0xx_ll_usb.o(.text.USB_EnableGlobalInt))
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Start
</UL>

<P><STRONG><a name="[3]"></a>after_scan_key</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, scan_key.o(.text.after_scan_key))
<BR>[Address Reference Count : 1]<UL><LI> scan_key.o(.data..L_MergedGlobals)
</UL>
<P><STRONG><a name="[2]"></a>before_scan_key</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, scan_key.o(.text.before_scan_key))
<BR>[Address Reference Count : 1]<UL><LI> scan_key.o(.data..L_MergedGlobals)
</UL>
<P><STRONG><a name="[96]"></a>key_event_get</STRONG> (Thumb, 316 bytes, Stack size 24 bytes, scan_key.o(.text.key_event_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = key_event_get
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_key_pgr
</UL>

<P><STRONG><a name="[0]"></a>key_flash_event</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, scan_key.o(.text.key_flash_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = key_flash_event
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_TogglePin
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scan_key.o(.data..L_MergedGlobals)
</UL>
<P><STRONG><a name="[1]"></a>key_mode_event</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, scan_key.o(.text.key_mode_event))
<BR>[Address Reference Count : 1]<UL><LI> scan_key.o(.data..L_MergedGlobals)
</UL>
<P><STRONG><a name="[47]"></a>main</STRONG> (Thumb, 134 bytes, Stack size 80 bytes, main.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = main &rArr; MX_USB_Device_Init &rArr; USBD_Init &rArr; USBD_LL_Init &rArr; HAL_PCD_Init &rArr; HAL_PCD_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_main
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USB_Device_Init
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI3_Init
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWREx_ControlVoltageScaling
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[99]"></a>my_main</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, my_main.o(.text.my_main))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + In Cycle
<LI>Call Chain = my_main &rArr;  my_main (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_main
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_main
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9b]"></a>scan_key_pgr</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, scan_key.o(.text.scan_key_pgr))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = scan_key_pgr &rArr; key_event_get
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_event_get
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_handler
</UL>

<P><STRONG><a name="[9a]"></a>task_handler</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, system.o(.text.task_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = task_handler &rArr; scan_key_pgr &rArr; key_event_get
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scan_key_pgr
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_main
</UL>

<P><STRONG><a name="[59]"></a>__aeabi_uidiv</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, aeabi_sdivfast.o(.text_divfast))
<BR><BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWREx_ControlVoltageScaling
</UL>

<P><STRONG><a name="[cd]"></a>__aeabi_idiv</STRONG> (Thumb, 434 bytes, Stack size 8 bytes, aeabi_sdivfast.o(.text_divfast), UNUSED)
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[15]"></a>CUSTOM_HID_Init_FS</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, usbd_custom_hid_if.o(.text.CUSTOM_HID_Init_FS))
<BR>[Address Reference Count : 1]<UL><LI> usbd_custom_hid_if.o(.data.USBD_CustomHID_fops_FS)
</UL>
<P><STRONG><a name="[16]"></a>CUSTOM_HID_DeInit_FS</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, usbd_custom_hid_if.o(.text.CUSTOM_HID_DeInit_FS))
<BR>[Address Reference Count : 1]<UL><LI> usbd_custom_hid_if.o(.data.USBD_CustomHID_fops_FS)
</UL>
<P><STRONG><a name="[17]"></a>CUSTOM_HID_OutEvent_FS</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, usbd_custom_hid_if.o(.text.CUSTOM_HID_OutEvent_FS))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = CUSTOM_HID_OutEvent_FS &rArr; USBD_CUSTOM_HID_ReceivePacket &rArr; USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CUSTOM_HID_ReceivePacket
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_custom_hid_if.o(.data.USBD_CustomHID_fops_FS)
</UL>
<P><STRONG><a name="[b]"></a>USBD_CUSTOM_HID_Init</STRONG> (Thumb, 158 bytes, Stack size 32 bytes, usbd_customhid.o(.text.USBD_CUSTOM_HID_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = USBD_CUSTOM_HID_Init &rArr; USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_static_malloc
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_PrepareReceive
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_OpenEP
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_customhid.o(.data.USBD_CUSTOM_HID)
</UL>
<P><STRONG><a name="[c]"></a>USBD_CUSTOM_HID_DeInit</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, usbd_customhid.o(.text.USBD_CUSTOM_HID_DeInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = USBD_CUSTOM_HID_DeInit &rArr; USBD_LL_CloseEP &rArr; HAL_PCD_EP_Close &rArr; USB_DeactivateEndpoint
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_static_free
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_CloseEP
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_customhid.o(.data.USBD_CUSTOM_HID)
</UL>
<P><STRONG><a name="[d]"></a>USBD_CUSTOM_HID_Setup</STRONG> (Thumb, 296 bytes, Stack size 32 bytes, usbd_customhid.o(.text.USBD_CUSTOM_HID_Setup))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = USBD_CUSTOM_HID_Setup &rArr; USBD_CtlPrepareRx &rArr; USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlPrepareRx
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlError
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendData
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_customhid.o(.data.USBD_CUSTOM_HID)
</UL>
<P><STRONG><a name="[e]"></a>USBD_CUSTOM_HID_EP0_RxReady</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, usbd_customhid.o(.text.USBD_CUSTOM_HID_EP0_RxReady))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USBD_CUSTOM_HID_EP0_RxReady
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_customhid.o(.data.USBD_CUSTOM_HID)
</UL>
<P><STRONG><a name="[f]"></a>USBD_CUSTOM_HID_DataIn</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, usbd_customhid.o(.text.USBD_CUSTOM_HID_DataIn))
<BR>[Address Reference Count : 1]<UL><LI> usbd_customhid.o(.data.USBD_CUSTOM_HID)
</UL>
<P><STRONG><a name="[10]"></a>USBD_CUSTOM_HID_DataOut</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, usbd_customhid.o(.text.USBD_CUSTOM_HID_DataOut))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USBD_CUSTOM_HID_DataOut
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_customhid.o(.data.USBD_CUSTOM_HID)
</UL>
<P><STRONG><a name="[11]"></a>USBD_CUSTOM_HID_GetHSCfgDesc</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, usbd_customhid.o(.text.USBD_CUSTOM_HID_GetHSCfgDesc))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USBD_CUSTOM_HID_GetHSCfgDesc &rArr; USBD_GetEpDesc
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetEpDesc
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_customhid.o(.data.USBD_CUSTOM_HID)
</UL>
<P><STRONG><a name="[12]"></a>USBD_CUSTOM_HID_GetFSCfgDesc</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, usbd_customhid.o(.text.USBD_CUSTOM_HID_GetFSCfgDesc))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USBD_CUSTOM_HID_GetFSCfgDesc &rArr; USBD_GetEpDesc
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetEpDesc
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_customhid.o(.data.USBD_CUSTOM_HID)
</UL>
<P><STRONG><a name="[13]"></a>USBD_CUSTOM_HID_GetOtherSpeedCfgDesc</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, usbd_customhid.o(.text.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USBD_CUSTOM_HID_GetOtherSpeedCfgDesc &rArr; USBD_GetEpDesc
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetEpDesc
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_customhid.o(.data.USBD_CUSTOM_HID)
</UL>
<P><STRONG><a name="[14]"></a>USBD_CUSTOM_HID_GetDeviceQualifierDesc</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_customhid.o(.text.USBD_CUSTOM_HID_GetDeviceQualifierDesc))
<BR>[Address Reference Count : 1]<UL><LI> usbd_customhid.o(.data.USBD_CUSTOM_HID)
</UL><P>
<H3>
Undefined Global Symbols
</H3>
<P><STRONG><a name="[53]"></a>_call_atexit_fns</STRONG> (ARM, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>
<HR></body></html>
