--cpu Cortex-M0+
"qa_g0\startup_stm32g0b1xx.o"
"qa_g0\main.o"
"qa_g0\gpio.o"
"qa_g0\spi.o"
"qa_g0\tim.o"
"qa_g0\stm32g0xx_it.o"
"qa_g0\stm32g0xx_hal_msp.o"
"qa_g0\usbd_conf.o"
"qa_g0\usb_device.o"
"qa_g0\usbd_desc.o"
"qa_g0\usbd_custom_hid_if.o"
"qa_g0\stm32g0xx_hal_pcd.o"
"qa_g0\stm32g0xx_hal_pcd_ex.o"
"qa_g0\stm32g0xx_ll_usb.o"
"qa_g0\stm32g0xx_hal_rcc.o"
"qa_g0\stm32g0xx_hal_rcc_ex.o"
"qa_g0\stm32g0xx_ll_rcc.o"
"qa_g0\stm32g0xx_hal_flash.o"
"qa_g0\stm32g0xx_hal_flash_ex.o"
"qa_g0\stm32g0xx_hal_gpio.o"
"qa_g0\stm32g0xx_hal_dma.o"
"qa_g0\stm32g0xx_hal_dma_ex.o"
"qa_g0\stm32g0xx_hal_pwr.o"
"qa_g0\stm32g0xx_hal_pwr_ex.o"
"qa_g0\stm32g0xx_hal_cortex.o"
"qa_g0\stm32g0xx_hal.o"
"qa_g0\stm32g0xx_hal_exti.o"
"qa_g0\stm32g0xx_hal_spi.o"
"qa_g0\stm32g0xx_hal_spi_ex.o"
"qa_g0\stm32g0xx_hal_tim.o"
"qa_g0\stm32g0xx_hal_tim_ex.o"
"qa_g0\system_stm32g0xx.o"
"qa_g0\usbd_core.o"
"qa_g0\usbd_ctlreq.o"
"qa_g0\usbd_ioreq.o"
"qa_g0\usbd_customhid.o"
"qa_g0\my_main.o"
"qa_g0\system.o"
"qa_g0\scan_key.o"
--strict --scatter "QA_G0\QA_G0.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "QA_G0.map" -o QA_G0\QA_G0.axf