<html>
<body>
<pre>
<h1>�Vision Build Log</h1>
<h2>Tool Versions:</h2>
IDE-Version: ��Vision V5.42.0.0
Copyright (C) 2025 ARM Ltd and ARM Germany GmbH. All rights reserved.
License Information: T L, TT, LIC=KRMW3-ADW47-562Q2-9WFX2-17VG9-5BRGU
 
Tool Versions:
Toolchain:       MDK-ARM Plus  Version: 5.42.0.0
Toolchain Path:  C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCLANG\Bin
C Compiler:      ArmClang.exe V6.23
Assembler:       Armasm.exe V6.23
Linker/Locator:  ArmLink.exe V6.23
Library Manager: ArmAr.exe V6.23
Hex Converter:   FromElf.exe V6.23
CPU DLL:         SARMCM3.DLL V5.42.0.0
Dialog DLL:      DARMCM1.DLL V1.19.6.0
Target DLL:      STLink\ST-LINKIII-KEIL_SWO.dll V3.3.0.0
Dialog DLL:      TARMCM1.DLL V1.14.6.0
 
<h2>Project:</h2>
D:\Develop\Neewer\Pre-research\Code\QA_G0\MDK-ARM\QA_G0.uvprojx
Project File Date:  08/15/2025

<h2>Output:</h2>
*** Using Compiler 'V6.23', folder: 'C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCLANG\Bin'
Build target 'QA_G0'
compiling system.c...
compiling scan_key.c...
linking...
Program Size: Code=13760 RO-data=284 RW-data=324 ZI-data=3900  
FromELF: creating hex file...
"QA_G0\QA_G0.axf" - 0 Error(s), 0 Warning(s).

<h2>Software Packages used:</h2>

Package Vendor: ARM
                https://www.keil.com/pack/ARM.CMSIS.6.1.0.pack
                ARM::CMSIS@6.1.0
                CMSIS (Common Microcontroller Software Interface Standard)
   * Component: CORE Version: 6.1.0

Package Vendor: Keil
                https://www.keil.com/pack/Keil.STM32G0xx_DFP.2.0.0.pack
                Keil::STM32G0xx_DFP@2.0.0
                STMicroelectronics STM32G0 Series Device Support

<h2>Collection of Component include folders:</h2>
  ./RTE/_QA_G0
  C:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

<h2>Collection of Component Files used:</h2>

   * Component: ARM::CMSIS:CORE@6.1.0
      Include file:  CMSIS/Core/Include/tz_context.h
Build Time Elapsed:  00:00:01
</pre>
</body>
</html>
