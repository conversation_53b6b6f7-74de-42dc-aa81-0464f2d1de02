Dependencies for Project 'QA_G0', Target 'QA_G0': (DO NOT MODIFY !)
CompilerVersion: 6230000::V6.23::ARMCLANG
F (startup_stm32g0b1xx.s)(0x689EE7C6)(--target=arm-arm-none-eabi -mcpu=cortex-m0plus -masm=auto  -Wa,armasm,--diag_suppress=A1950W -c

-gdwarf-4

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-Wa,armasm,--pd,"__UVISION_VERSION SETA 542" -Wa,armasm,--pd,"STM32G0B1xx SETA 1" -Wa,armasm,--pd,"_RTE_ SETA 1"

-o qa_g0/startup_stm32g0b1xx.o)
F (../Core/Src/main.c)(0x689EE60D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/main.o -MMD)
I (..\Core\Inc\main.h)(0x689EE7C4)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
I (..\Core\Inc\spi.h)(0x689ED6E0)
I (..\Core\Inc\tim.h)(0x689ED6E0)
I (..\USB_Device\App\usb_device.h)(0x689ED6E0)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_def.h)(0x65FA5B21)
I (..\USB_Device\Target\usbd_conf.h)(0x689ED6E0)
I (..\Core\Inc\gpio.h)(0x689ED6DF)
I (..\User\Inc\my_main.h)(0x689EE4A2)
F (../Core/Src/gpio.c)(0x689EE7C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/gpio.o -MMD)
I (..\Core\Inc\gpio.h)(0x689ED6DF)
I (..\Core\Inc\main.h)(0x689EE7C4)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../Core/Src/spi.c)(0x689ED6E0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/spi.o -MMD)
I (..\Core\Inc\spi.h)(0x689ED6E0)
I (..\Core\Inc\main.h)(0x689EE7C4)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../Core/Src/tim.c)(0x689ED6E0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/tim.o -MMD)
I (..\Core\Inc\tim.h)(0x689ED6E0)
I (..\Core\Inc\main.h)(0x689EE7C4)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../Core/Src/stm32g0xx_it.c)(0x689ED6E1)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/stm32g0xx_it.o -MMD)
I (..\Core\Inc\main.h)(0x689EE7C4)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_it.h)(0x689ED6E1)
F (../Core/Src/stm32g0xx_hal_msp.c)(0x689ED6E1)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/stm32g0xx_hal_msp.o -MMD)
I (..\Core\Inc\main.h)(0x689EE7C4)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../USB_Device/Target/usbd_conf.c)(0x689ED6E0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/usbd_conf.o -MMD)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_def.h)(0x65FA5B21)
I (..\USB_Device\Target\usbd_conf.h)(0x689ED6E0)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_core.h)(0x65FA5B21)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ioreq.h)(0x65FA5B21)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ctlreq.h)(0x65FA5B21)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\CustomHID\Inc\usbd_customhid.h)(0x65FA5B21)
F (../USB_Device/App/usb_device.c)(0x689ED6E0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/usb_device.o -MMD)
I (..\USB_Device\App\usb_device.h)(0x689ED6E0)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_def.h)(0x65FA5B21)
I (..\USB_Device\Target\usbd_conf.h)(0x689ED6E0)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_core.h)(0x65FA5B21)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ioreq.h)(0x65FA5B21)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ctlreq.h)(0x65FA5B21)
I (..\USB_Device\App\usbd_desc.h)(0x689ED6E0)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\CustomHID\Inc\usbd_customhid.h)(0x65FA5B21)
I (..\USB_Device\App\usbd_custom_hid_if.h)(0x689ED6E0)
F (../USB_Device/App/usbd_desc.c)(0x689ED6E0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/usbd_desc.o -MMD)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_core.h)(0x65FA5B21)
I (..\USB_Device\Target\usbd_conf.h)(0x689ED6E0)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_def.h)(0x65FA5B21)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ioreq.h)(0x65FA5B21)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ctlreq.h)(0x65FA5B21)
I (..\USB_Device\App\usbd_desc.h)(0x689ED6E0)
F (../USB_Device/App/usbd_custom_hid_if.c)(0x689ED6E0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/usbd_custom_hid_if.o -MMD)
I (..\USB_Device\App\usbd_custom_hid_if.h)(0x689ED6E0)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\CustomHID\Inc\usbd_customhid.h)(0x65FA5B21)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ioreq.h)(0x65FA5B21)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_def.h)(0x65FA5B21)
I (..\USB_Device\Target\usbd_conf.h)(0x689ED6E0)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_core.h)(0x65FA5B21)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ctlreq.h)(0x65FA5B21)
F (../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_pcd.c)(0x65FA5B32)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/stm32g0xx_hal_pcd.o -MMD)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_pcd_ex.c)(0x65FA5B32)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/stm32g0xx_hal_pcd_ex.o -MMD)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_ll_usb.c)(0x65FA5B32)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/stm32g0xx_ll_usb.o -MMD)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_rcc.c)(0x65FA5B32)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/stm32g0xx_hal_rcc.o -MMD)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_rcc_ex.c)(0x65FA5B32)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/stm32g0xx_hal_rcc_ex.o -MMD)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_ll_rcc.c)(0x65FA5B32)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/stm32g0xx_ll_rcc.o -MMD)
F (../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_flash.c)(0x65FA5B32)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/stm32g0xx_hal_flash.o -MMD)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_flash_ex.c)(0x65FA5B32)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/stm32g0xx_hal_flash_ex.o -MMD)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_gpio.c)(0x65FA5B32)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/stm32g0xx_hal_gpio.o -MMD)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_dma.c)(0x65FA5B32)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/stm32g0xx_hal_dma.o -MMD)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_dma_ex.c)(0x65FA5B32)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/stm32g0xx_hal_dma_ex.o -MMD)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_pwr.c)(0x65FA5B32)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/stm32g0xx_hal_pwr.o -MMD)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_pwr_ex.c)(0x65FA5B32)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/stm32g0xx_hal_pwr_ex.o -MMD)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_cortex.c)(0x65FA5B32)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/stm32g0xx_hal_cortex.o -MMD)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal.c)(0x65FA5B32)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/stm32g0xx_hal.o -MMD)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_exti.c)(0x65FA5B32)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/stm32g0xx_hal_exti.o -MMD)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_spi.c)(0x65FA5B32)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/stm32g0xx_hal_spi.o -MMD)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_spi_ex.c)(0x65FA5B32)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/stm32g0xx_hal_spi_ex.o -MMD)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_tim.c)(0x65FA5B32)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/stm32g0xx_hal_tim.o -MMD)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_tim_ex.c)(0x65FA5B32)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/stm32g0xx_hal_tim_ex.o -MMD)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../Core/Src/system_stm32g0xx.c)(0x65FA5B32)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/system_stm32g0xx.o -MMD)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (../Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_core.c)(0x65FA5B21)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/usbd_core.o -MMD)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_core.h)(0x65FA5B21)
I (..\USB_Device\Target\usbd_conf.h)(0x689ED6E0)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_def.h)(0x65FA5B21)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ioreq.h)(0x65FA5B21)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ctlreq.h)(0x65FA5B21)
F (../Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_ctlreq.c)(0x65FA5B21)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/usbd_ctlreq.o -MMD)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ctlreq.h)(0x65FA5B21)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_def.h)(0x65FA5B21)
I (..\USB_Device\Target\usbd_conf.h)(0x689ED6E0)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ioreq.h)(0x65FA5B21)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_core.h)(0x65FA5B21)
F (../Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_ioreq.c)(0x65FA5B21)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/usbd_ioreq.o -MMD)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ioreq.h)(0x65FA5B21)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_def.h)(0x65FA5B21)
I (..\USB_Device\Target\usbd_conf.h)(0x689ED6E0)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_core.h)(0x65FA5B21)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ctlreq.h)(0x65FA5B21)
F (../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Src/usbd_customhid.c)(0x65FA5B21)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/usbd_customhid.o -MMD)
I (..\Middlewares\ST\STM32_USB_Device_Library\Class\CustomHID\Inc\usbd_customhid.h)(0x65FA5B21)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ioreq.h)(0x65FA5B21)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_def.h)(0x65FA5B21)
I (..\USB_Device\Target\usbd_conf.h)(0x689ED6E0)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_core.h)(0x65FA5B21)
I (..\Middlewares\ST\STM32_USB_Device_Library\Core\Inc\usbd_ctlreq.h)(0x65FA5B21)
F (..\User\Src\my_main.c)(0x689EE509)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/my_main.o -MMD)
I (..\User\Inc\my_main.h)(0x689EE4A2)
I (..\User\Inc\system.h)(0x689EE50A)
I (..\Core\Inc\main.h)(0x689EE7C4)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
F (..\User\Src\system.c)(0x689EE96F)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/system.o -MMD)
I (..\User\Inc\system.h)(0x689EE50A)
I (..\Core\Inc\main.h)(0x689EE7C4)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
I (..\User\Inc\scan_key.h)(0x689EE9CB)
I (..\Core\Inc\tim.h)(0x689ED6E0)
F (..\User\Src\scan_key.c)(0x689EE96F)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../USB_Device/App -I ../USB_Device/Target -I ../Drivers/STM32G0xx_HAL_Driver/Inc -I ../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Device_Library/Class/CustomHID/Inc -I ../Drivers/CMSIS/Device/ST/STM32G0xx/Include -I ../Drivers/CMSIS/Include -I ../User/Inc

-I./RTE/_QA_G0

-IC:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32G0B1xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32G0B1xx

-o qa_g0/scan_key.o -MMD)
I (..\User\Inc\scan_key.h)(0x689EE9CB)
I (..\Core\Inc\main.h)(0x689EE7C4)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal.h)(0x65FA5B32)
I (..\Core\Inc\stm32g0xx_hal_conf.h)(0x689ED6E1)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_def.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\stm32g0b1xx.h)(0x65FA5B32)
I (..\Drivers\CMSIS\Include\core_cm0plus.h)(0x65FA5B21)
I (..\Drivers\CMSIS\Device\ST\STM32G0xx\Include\system_stm32g0xx.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_rcc.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_rcc_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_gpio_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dma.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_dmamux.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_dma_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_cortex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_exti.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_flash_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_ll_usb.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pcd_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_pwr_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_spi_ex.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim.h)(0x65FA5B32)
I (..\Drivers\STM32G0xx_HAL_Driver\Inc\stm32g0xx_hal_tim_ex.h)(0x65FA5B32)
I (..\User\Inc\system.h)(0x689EE50A)
