Component: Arm Compiler for Embedded 6.23 Tool: armlink [5f102400]

==============================================================================

Section Cross References

    startup_stm32g0b1xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32g0b1xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32g0b1xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32g0b1xx.o(RESET) refers to startup_stm32g0b1xx.o(STACK) for __initial_sp
    startup_stm32g0b1xx.o(RESET) refers to startup_stm32g0b1xx.o(.text) for Reset_Handler
    startup_stm32g0b1xx.o(RESET) refers to stm32g0xx_it.o(.text.NMI_Handler) for NMI_Handler
    startup_stm32g0b1xx.o(RESET) refers to stm32g0xx_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_stm32g0b1xx.o(RESET) refers to stm32g0xx_it.o(.text.SVC_Handler) for SVC_Handler
    startup_stm32g0b1xx.o(RESET) refers to stm32g0xx_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_stm32g0b1xx.o(RESET) refers to stm32g0xx_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_stm32g0b1xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32g0b1xx.o(.text) refers to system_stm32g0xx.o(.text.SystemInit) for SystemInit
    startup_stm32g0b1xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32g0b1xx.o(.text) refers to startup_stm32g0b1xx.o(HEAP) for Heap_Mem
    startup_stm32g0b1xx.o(.text) refers to startup_stm32g0b1xx.o(STACK) for Stack_Mem
    main.o(.text.main) refers to stm32g0xx_hal.o(.text.HAL_Init) for HAL_Init
    main.o(.text.main) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) for HAL_PWREx_ControlVoltageScaling
    main.o(.text.main) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(.text.main) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(.text.main) refers to gpio.o(.text.MX_GPIO_Init) for MX_GPIO_Init
    main.o(.text.main) refers to spi.o(.text.MX_SPI3_Init) for MX_SPI3_Init
    main.o(.text.main) refers to usb_device.o(.text.MX_USB_Device_Init) for MX_USB_Device_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM6_Init) for MX_TIM6_Init
    main.o(.text.main) refers to my_main.o(.text.my_main) for my_main
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.text.SystemClock_Config) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) for HAL_PWREx_ControlVoltageScaling
    main.o(.text.SystemClock_Config) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(.text.SystemClock_Config) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(.ARM.exidx.text.SystemClock_Config) refers to main.o(.text.SystemClock_Config) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.Error_Handler) refers to main.o(.text.Error_Handler) for [Anonymous Symbol]
    gpio.o(.text.MX_GPIO_Init) refers to stm32g0xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(.text.MX_GPIO_Init) refers to stm32g0xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(.ARM.exidx.text.MX_GPIO_Init) refers to gpio.o(.text.MX_GPIO_Init) for [Anonymous Symbol]
    spi.o(.text.MX_SPI3_Init) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_Init) for HAL_SPI_Init
    spi.o(.text.MX_SPI3_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    spi.o(.text.MX_SPI3_Init) refers to spi.o(.bss.hspi3) for hspi3
    spi.o(.ARM.exidx.text.MX_SPI3_Init) refers to spi.o(.text.MX_SPI3_Init) for [Anonymous Symbol]
    spi.o(.text.HAL_SPI_MspInit) refers to stm32g0xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    spi.o(.ARM.exidx.text.HAL_SPI_MspInit) refers to spi.o(.text.HAL_SPI_MspInit) for [Anonymous Symbol]
    spi.o(.text.HAL_SPI_MspDeInit) refers to stm32g0xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    spi.o(.ARM.exidx.text.HAL_SPI_MspDeInit) refers to spi.o(.text.HAL_SPI_MspDeInit) for [Anonymous Symbol]
    tim.o(.text.MX_TIM6_Init) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(.text.MX_TIM6_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM6_Init) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.text.MX_TIM6_Init) refers to tim.o(.bss.htim6) for htim6
    tim.o(.ARM.exidx.text.MX_TIM6_Init) refers to tim.o(.text.MX_TIM6_Init) for [Anonymous Symbol]
    tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit) refers to tim.o(.text.HAL_TIM_Base_MspInit) for [Anonymous Symbol]
    tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit) refers to tim.o(.text.HAL_TIM_Base_MspDeInit) for [Anonymous Symbol]
    stm32g0xx_it.o(.ARM.exidx.text.NMI_Handler) refers to stm32g0xx_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    stm32g0xx_it.o(.ARM.exidx.text.HardFault_Handler) refers to stm32g0xx_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    stm32g0xx_it.o(.ARM.exidx.text.SVC_Handler) refers to stm32g0xx_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    stm32g0xx_it.o(.ARM.exidx.text.PendSV_Handler) refers to stm32g0xx_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    stm32g0xx_it.o(.text.SysTick_Handler) refers to system.o(.text.HAL_IncTick) for HAL_IncTick
    stm32g0xx_it.o(.ARM.exidx.text.SysTick_Handler) refers to stm32g0xx_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    stm32g0xx_hal_msp.o(.text.HAL_MspInit) refers to stm32g0xx_hal.o(.text.HAL_SYSCFG_StrobeDBattpinsConfig) for HAL_SYSCFG_StrobeDBattpinsConfig
    stm32g0xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit) refers to stm32g0xx_hal_msp.o(.text.HAL_MspInit) for [Anonymous Symbol]
    usbd_conf.o(.text.HAL_PCD_MspInit) refers to rt_memclr.o(.text) for __aeabi_memclr4
    usbd_conf.o(.text.HAL_PCD_MspInit) refers to stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    usbd_conf.o(.text.HAL_PCD_MspInit) refers to main.o(.text.Error_Handler) for Error_Handler
    usbd_conf.o(.text.HAL_PCD_MspInit) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableVddUSB) for HAL_PWREx_EnableVddUSB
    usbd_conf.o(.ARM.exidx.text.HAL_PCD_MspInit) refers to usbd_conf.o(.text.HAL_PCD_MspInit) for [Anonymous Symbol]
    usbd_conf.o(.ARM.exidx.text.HAL_PCD_MspDeInit) refers to usbd_conf.o(.text.HAL_PCD_MspDeInit) for [Anonymous Symbol]
    usbd_conf.o(.text.HAL_PCD_SetupStageCallback) refers to usbd_core.o(.text.USBD_LL_SetupStage) for USBD_LL_SetupStage
    usbd_conf.o(.ARM.exidx.text.HAL_PCD_SetupStageCallback) refers to usbd_conf.o(.text.HAL_PCD_SetupStageCallback) for [Anonymous Symbol]
    usbd_conf.o(.text.HAL_PCD_DataOutStageCallback) refers to usbd_core.o(.text.USBD_LL_DataOutStage) for USBD_LL_DataOutStage
    usbd_conf.o(.ARM.exidx.text.HAL_PCD_DataOutStageCallback) refers to usbd_conf.o(.text.HAL_PCD_DataOutStageCallback) for [Anonymous Symbol]
    usbd_conf.o(.text.HAL_PCD_DataInStageCallback) refers to usbd_core.o(.text.USBD_LL_DataInStage) for USBD_LL_DataInStage
    usbd_conf.o(.ARM.exidx.text.HAL_PCD_DataInStageCallback) refers to usbd_conf.o(.text.HAL_PCD_DataInStageCallback) for [Anonymous Symbol]
    usbd_conf.o(.text.HAL_PCD_SOFCallback) refers to usbd_core.o(.text.USBD_LL_SOF) for USBD_LL_SOF
    usbd_conf.o(.ARM.exidx.text.HAL_PCD_SOFCallback) refers to usbd_conf.o(.text.HAL_PCD_SOFCallback) for [Anonymous Symbol]
    usbd_conf.o(.text.HAL_PCD_ResetCallback) refers to main.o(.text.Error_Handler) for Error_Handler
    usbd_conf.o(.text.HAL_PCD_ResetCallback) refers to usbd_core.o(.text.USBD_LL_SetSpeed) for USBD_LL_SetSpeed
    usbd_conf.o(.text.HAL_PCD_ResetCallback) refers to usbd_core.o(.text.USBD_LL_Reset) for USBD_LL_Reset
    usbd_conf.o(.ARM.exidx.text.HAL_PCD_ResetCallback) refers to usbd_conf.o(.text.HAL_PCD_ResetCallback) for [Anonymous Symbol]
    usbd_conf.o(.text.HAL_PCD_SuspendCallback) refers to usbd_core.o(.text.USBD_LL_Suspend) for USBD_LL_Suspend
    usbd_conf.o(.ARM.exidx.text.HAL_PCD_SuspendCallback) refers to usbd_conf.o(.text.HAL_PCD_SuspendCallback) for [Anonymous Symbol]
    usbd_conf.o(.text.HAL_PCD_ResumeCallback) refers to main.o(.text.SystemClock_Config) for SystemClock_Config
    usbd_conf.o(.text.HAL_PCD_ResumeCallback) refers to usbd_core.o(.text.USBD_LL_Resume) for USBD_LL_Resume
    usbd_conf.o(.ARM.exidx.text.HAL_PCD_ResumeCallback) refers to usbd_conf.o(.text.HAL_PCD_ResumeCallback) for [Anonymous Symbol]
    usbd_conf.o(.text.HAL_PCD_ISOOUTIncompleteCallback) refers to usbd_core.o(.text.USBD_LL_IsoOUTIncomplete) for USBD_LL_IsoOUTIncomplete
    usbd_conf.o(.ARM.exidx.text.HAL_PCD_ISOOUTIncompleteCallback) refers to usbd_conf.o(.text.HAL_PCD_ISOOUTIncompleteCallback) for [Anonymous Symbol]
    usbd_conf.o(.text.HAL_PCD_ISOINIncompleteCallback) refers to usbd_core.o(.text.USBD_LL_IsoINIncomplete) for USBD_LL_IsoINIncomplete
    usbd_conf.o(.ARM.exidx.text.HAL_PCD_ISOINIncompleteCallback) refers to usbd_conf.o(.text.HAL_PCD_ISOINIncompleteCallback) for [Anonymous Symbol]
    usbd_conf.o(.text.HAL_PCD_ConnectCallback) refers to usbd_core.o(.text.USBD_LL_DevConnected) for USBD_LL_DevConnected
    usbd_conf.o(.ARM.exidx.text.HAL_PCD_ConnectCallback) refers to usbd_conf.o(.text.HAL_PCD_ConnectCallback) for [Anonymous Symbol]
    usbd_conf.o(.text.HAL_PCD_DisconnectCallback) refers to usbd_core.o(.text.USBD_LL_DevDisconnected) for USBD_LL_DevDisconnected
    usbd_conf.o(.ARM.exidx.text.HAL_PCD_DisconnectCallback) refers to usbd_conf.o(.text.HAL_PCD_DisconnectCallback) for [Anonymous Symbol]
    usbd_conf.o(.text.USBD_LL_Init) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_Init) for HAL_PCD_Init
    usbd_conf.o(.text.USBD_LL_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usbd_conf.o(.text.USBD_LL_Init) refers to stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_PMAConfig) for HAL_PCDEx_PMAConfig
    usbd_conf.o(.text.USBD_LL_Init) refers to usbd_conf.o(.bss.hpcd_USB_DRD_FS) for hpcd_USB_DRD_FS
    usbd_conf.o(.ARM.exidx.text.USBD_LL_Init) refers to usbd_conf.o(.text.USBD_LL_Init) for [Anonymous Symbol]
    usbd_conf.o(.text.USBD_LL_DeInit) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_DeInit) for HAL_PCD_DeInit
    usbd_conf.o(.ARM.exidx.text.USBD_LL_DeInit) refers to usbd_conf.o(.text.USBD_LL_DeInit) for [Anonymous Symbol]
    usbd_conf.o(.text.USBD_LL_Start) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_Start) for HAL_PCD_Start
    usbd_conf.o(.ARM.exidx.text.USBD_LL_Start) refers to usbd_conf.o(.text.USBD_LL_Start) for [Anonymous Symbol]
    usbd_conf.o(.text.USBD_LL_Stop) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_Stop) for HAL_PCD_Stop
    usbd_conf.o(.ARM.exidx.text.USBD_LL_Stop) refers to usbd_conf.o(.text.USBD_LL_Stop) for [Anonymous Symbol]
    usbd_conf.o(.text.USBD_LL_OpenEP) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Open) for HAL_PCD_EP_Open
    usbd_conf.o(.ARM.exidx.text.USBD_LL_OpenEP) refers to usbd_conf.o(.text.USBD_LL_OpenEP) for [Anonymous Symbol]
    usbd_conf.o(.text.USBD_LL_CloseEP) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Close) for HAL_PCD_EP_Close
    usbd_conf.o(.ARM.exidx.text.USBD_LL_CloseEP) refers to usbd_conf.o(.text.USBD_LL_CloseEP) for [Anonymous Symbol]
    usbd_conf.o(.text.USBD_LL_FlushEP) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Flush) for HAL_PCD_EP_Flush
    usbd_conf.o(.ARM.exidx.text.USBD_LL_FlushEP) refers to usbd_conf.o(.text.USBD_LL_FlushEP) for [Anonymous Symbol]
    usbd_conf.o(.text.USBD_LL_StallEP) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_SetStall) for HAL_PCD_EP_SetStall
    usbd_conf.o(.ARM.exidx.text.USBD_LL_StallEP) refers to usbd_conf.o(.text.USBD_LL_StallEP) for [Anonymous Symbol]
    usbd_conf.o(.text.USBD_LL_ClearStallEP) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_ClrStall) for HAL_PCD_EP_ClrStall
    usbd_conf.o(.ARM.exidx.text.USBD_LL_ClearStallEP) refers to usbd_conf.o(.text.USBD_LL_ClearStallEP) for [Anonymous Symbol]
    usbd_conf.o(.ARM.exidx.text.USBD_LL_IsStallEP) refers to usbd_conf.o(.text.USBD_LL_IsStallEP) for [Anonymous Symbol]
    usbd_conf.o(.text.USBD_LL_SetUSBAddress) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_SetAddress) for HAL_PCD_SetAddress
    usbd_conf.o(.ARM.exidx.text.USBD_LL_SetUSBAddress) refers to usbd_conf.o(.text.USBD_LL_SetUSBAddress) for [Anonymous Symbol]
    usbd_conf.o(.text.USBD_LL_Transmit) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Transmit) for HAL_PCD_EP_Transmit
    usbd_conf.o(.ARM.exidx.text.USBD_LL_Transmit) refers to usbd_conf.o(.text.USBD_LL_Transmit) for [Anonymous Symbol]
    usbd_conf.o(.text.USBD_LL_PrepareReceive) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Receive) for HAL_PCD_EP_Receive
    usbd_conf.o(.ARM.exidx.text.USBD_LL_PrepareReceive) refers to usbd_conf.o(.text.USBD_LL_PrepareReceive) for [Anonymous Symbol]
    usbd_conf.o(.text.USBD_LL_GetRxDataSize) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_GetRxCount) for HAL_PCD_EP_GetRxCount
    usbd_conf.o(.ARM.exidx.text.USBD_LL_GetRxDataSize) refers to usbd_conf.o(.text.USBD_LL_GetRxDataSize) for [Anonymous Symbol]
    usbd_conf.o(.text.HAL_PCDEx_LPM_Callback) refers to main.o(.text.SystemClock_Config) for SystemClock_Config
    usbd_conf.o(.text.HAL_PCDEx_LPM_Callback) refers to usbd_core.o(.text.USBD_LL_Resume) for USBD_LL_Resume
    usbd_conf.o(.text.HAL_PCDEx_LPM_Callback) refers to usbd_core.o(.text.USBD_LL_Suspend) for USBD_LL_Suspend
    usbd_conf.o(.ARM.exidx.text.HAL_PCDEx_LPM_Callback) refers to usbd_conf.o(.text.HAL_PCDEx_LPM_Callback) for [Anonymous Symbol]
    usbd_conf.o(.text.USBD_static_malloc) refers to usbd_conf.o(.bss.USBD_static_malloc.mem) for [Anonymous Symbol]
    usbd_conf.o(.ARM.exidx.text.USBD_static_malloc) refers to usbd_conf.o(.text.USBD_static_malloc) for [Anonymous Symbol]
    usbd_conf.o(.ARM.exidx.text.USBD_static_free) refers to usbd_conf.o(.text.USBD_static_free) for [Anonymous Symbol]
    usbd_conf.o(.text.USBD_LL_Delay) refers to stm32g0xx_hal.o(.text.HAL_Delay) for HAL_Delay
    usbd_conf.o(.ARM.exidx.text.USBD_LL_Delay) refers to usbd_conf.o(.text.USBD_LL_Delay) for [Anonymous Symbol]
    usb_device.o(.text.MX_USB_Device_Init) refers to usbd_core.o(.text.USBD_Init) for USBD_Init
    usb_device.o(.text.MX_USB_Device_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usb_device.o(.text.MX_USB_Device_Init) refers to usbd_core.o(.text.USBD_RegisterClass) for USBD_RegisterClass
    usb_device.o(.text.MX_USB_Device_Init) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_RegisterInterface) for USBD_CUSTOM_HID_RegisterInterface
    usb_device.o(.text.MX_USB_Device_Init) refers to usbd_core.o(.text.USBD_Start) for USBD_Start
    usb_device.o(.text.MX_USB_Device_Init) refers to usb_device.o(.bss.hUsbDeviceFS) for hUsbDeviceFS
    usb_device.o(.text.MX_USB_Device_Init) refers to usbd_desc.o(.data.CUSTOM_HID_Desc) for CUSTOM_HID_Desc
    usb_device.o(.text.MX_USB_Device_Init) refers to usbd_customhid.o(.data.USBD_CUSTOM_HID) for USBD_CUSTOM_HID
    usb_device.o(.text.MX_USB_Device_Init) refers to usbd_custom_hid_if.o(.data.USBD_CustomHID_fops_FS) for USBD_CustomHID_fops_FS
    usb_device.o(.ARM.exidx.text.MX_USB_Device_Init) refers to usb_device.o(.text.MX_USB_Device_Init) for [Anonymous Symbol]
    usbd_desc.o(.text.USBD_CUSTOM_HID_DeviceDescriptor) refers to usbd_desc.o(.data.USBD_CUSTOM_HID_DeviceDesc) for USBD_CUSTOM_HID_DeviceDesc
    usbd_desc.o(.ARM.exidx.text.USBD_CUSTOM_HID_DeviceDescriptor) refers to usbd_desc.o(.text.USBD_CUSTOM_HID_DeviceDescriptor) for [Anonymous Symbol]
    usbd_desc.o(.text.USBD_CUSTOM_HID_LangIDStrDescriptor) refers to usbd_desc.o(.data.USBD_LangIDDesc) for USBD_LangIDDesc
    usbd_desc.o(.ARM.exidx.text.USBD_CUSTOM_HID_LangIDStrDescriptor) refers to usbd_desc.o(.text.USBD_CUSTOM_HID_LangIDStrDescriptor) for [Anonymous Symbol]
    usbd_desc.o(.text.USBD_CUSTOM_HID_ManufacturerStrDescriptor) refers to usbd_ctlreq.o(.text.USBD_GetString) for USBD_GetString
    usbd_desc.o(.text.USBD_CUSTOM_HID_ManufacturerStrDescriptor) refers to usbd_desc.o(.bss.USBD_StrDesc) for USBD_StrDesc
    usbd_desc.o(.ARM.exidx.text.USBD_CUSTOM_HID_ManufacturerStrDescriptor) refers to usbd_desc.o(.text.USBD_CUSTOM_HID_ManufacturerStrDescriptor) for [Anonymous Symbol]
    usbd_desc.o(.text.USBD_CUSTOM_HID_ProductStrDescriptor) refers to usbd_ctlreq.o(.text.USBD_GetString) for USBD_GetString
    usbd_desc.o(.text.USBD_CUSTOM_HID_ProductStrDescriptor) refers to usbd_desc.o(.bss.USBD_StrDesc) for USBD_StrDesc
    usbd_desc.o(.ARM.exidx.text.USBD_CUSTOM_HID_ProductStrDescriptor) refers to usbd_desc.o(.text.USBD_CUSTOM_HID_ProductStrDescriptor) for [Anonymous Symbol]
    usbd_desc.o(.text.USBD_CUSTOM_HID_SerialStrDescriptor) refers to usbd_desc.o(.data.USBD_StringSerial) for USBD_StringSerial
    usbd_desc.o(.ARM.exidx.text.USBD_CUSTOM_HID_SerialStrDescriptor) refers to usbd_desc.o(.text.USBD_CUSTOM_HID_SerialStrDescriptor) for [Anonymous Symbol]
    usbd_desc.o(.text.USBD_CUSTOM_HID_ConfigStrDescriptor) refers to usbd_ctlreq.o(.text.USBD_GetString) for USBD_GetString
    usbd_desc.o(.text.USBD_CUSTOM_HID_ConfigStrDescriptor) refers to usbd_desc.o(.bss.USBD_StrDesc) for USBD_StrDesc
    usbd_desc.o(.ARM.exidx.text.USBD_CUSTOM_HID_ConfigStrDescriptor) refers to usbd_desc.o(.text.USBD_CUSTOM_HID_ConfigStrDescriptor) for [Anonymous Symbol]
    usbd_desc.o(.text.USBD_CUSTOM_HID_InterfaceStrDescriptor) refers to usbd_ctlreq.o(.text.USBD_GetString) for USBD_GetString
    usbd_desc.o(.text.USBD_CUSTOM_HID_InterfaceStrDescriptor) refers to usbd_desc.o(.bss.USBD_StrDesc) for USBD_StrDesc
    usbd_desc.o(.ARM.exidx.text.USBD_CUSTOM_HID_InterfaceStrDescriptor) refers to usbd_desc.o(.text.USBD_CUSTOM_HID_InterfaceStrDescriptor) for [Anonymous Symbol]
    usbd_desc.o(.data.CUSTOM_HID_Desc) refers to usbd_desc.o(.text.USBD_CUSTOM_HID_DeviceDescriptor) for USBD_CUSTOM_HID_DeviceDescriptor
    usbd_desc.o(.data.CUSTOM_HID_Desc) refers to usbd_desc.o(.text.USBD_CUSTOM_HID_LangIDStrDescriptor) for USBD_CUSTOM_HID_LangIDStrDescriptor
    usbd_desc.o(.data.CUSTOM_HID_Desc) refers to usbd_desc.o(.text.USBD_CUSTOM_HID_ManufacturerStrDescriptor) for USBD_CUSTOM_HID_ManufacturerStrDescriptor
    usbd_desc.o(.data.CUSTOM_HID_Desc) refers to usbd_desc.o(.text.USBD_CUSTOM_HID_ProductStrDescriptor) for USBD_CUSTOM_HID_ProductStrDescriptor
    usbd_desc.o(.data.CUSTOM_HID_Desc) refers to usbd_desc.o(.text.USBD_CUSTOM_HID_SerialStrDescriptor) for USBD_CUSTOM_HID_SerialStrDescriptor
    usbd_desc.o(.data.CUSTOM_HID_Desc) refers to usbd_desc.o(.text.USBD_CUSTOM_HID_ConfigStrDescriptor) for USBD_CUSTOM_HID_ConfigStrDescriptor
    usbd_desc.o(.data.CUSTOM_HID_Desc) refers to usbd_desc.o(.text.USBD_CUSTOM_HID_InterfaceStrDescriptor) for USBD_CUSTOM_HID_InterfaceStrDescriptor
    usbd_custom_hid_if.o(.ARM.exidx.text.CUSTOM_HID_Init_FS) refers to usbd_custom_hid_if.o(.text.CUSTOM_HID_Init_FS) for [Anonymous Symbol]
    usbd_custom_hid_if.o(.ARM.exidx.text.CUSTOM_HID_DeInit_FS) refers to usbd_custom_hid_if.o(.text.CUSTOM_HID_DeInit_FS) for [Anonymous Symbol]
    usbd_custom_hid_if.o(.text.CUSTOM_HID_OutEvent_FS) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_ReceivePacket) for USBD_CUSTOM_HID_ReceivePacket
    usbd_custom_hid_if.o(.text.CUSTOM_HID_OutEvent_FS) refers to usb_device.o(.bss.hUsbDeviceFS) for hUsbDeviceFS
    usbd_custom_hid_if.o(.ARM.exidx.text.CUSTOM_HID_OutEvent_FS) refers to usbd_custom_hid_if.o(.text.CUSTOM_HID_OutEvent_FS) for [Anonymous Symbol]
    usbd_custom_hid_if.o(.data.USBD_CustomHID_fops_FS) refers to usbd_custom_hid_if.o(.data.CUSTOM_HID_ReportDesc_FS) for [Anonymous Symbol]
    usbd_custom_hid_if.o(.data.USBD_CustomHID_fops_FS) refers to usbd_custom_hid_if.o(.text.CUSTOM_HID_Init_FS) for CUSTOM_HID_Init_FS
    usbd_custom_hid_if.o(.data.USBD_CustomHID_fops_FS) refers to usbd_custom_hid_if.o(.text.CUSTOM_HID_DeInit_FS) for CUSTOM_HID_DeInit_FS
    usbd_custom_hid_if.o(.data.USBD_CustomHID_fops_FS) refers to usbd_custom_hid_if.o(.text.CUSTOM_HID_OutEvent_FS) for CUSTOM_HID_OutEvent_FS
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_Init) refers to usbd_conf.o(.text.HAL_PCD_MspInit) for HAL_PCD_MspInit
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_Init) refers to stm32g0xx_ll_usb.o(.text.USB_DisableGlobalInt) for USB_DisableGlobalInt
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_Init) refers to stm32g0xx_ll_usb.o(.text.USB_DevInit) for USB_DevInit
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_Init) refers to stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_ActivateLPM) for HAL_PCDEx_ActivateLPM
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_Init) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_Init) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_MspInit) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_MspInit) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_DeInit) refers to stm32g0xx_ll_usb.o(.text.USB_StopDevice) for USB_StopDevice
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_DeInit) refers to usbd_conf.o(.text.HAL_PCD_MspDeInit) for HAL_PCD_MspDeInit
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_DeInit) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_DeInit) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_MspDeInit) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_MspDeInit) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_Start) refers to stm32g0xx_ll_usb.o(.text.USB_EnableGlobalInt) for USB_EnableGlobalInt
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_Start) refers to stm32g0xx_ll_usb.o(.text.USB_DevConnect) for USB_DevConnect
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_Start) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_Start) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_Stop) refers to stm32g0xx_ll_usb.o(.text.USB_DisableGlobalInt) for USB_DisableGlobalInt
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_Stop) refers to stm32g0xx_ll_usb.o(.text.USB_DevDisconnect) for USB_DevDisconnect
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_Stop) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_Stop) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_IRQHandler) refers to stm32g0xx_ll_usb.o(.text.USB_ReadInterrupts) for USB_ReadInterrupts
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_IRQHandler) refers to usbd_conf.o(.text.HAL_PCD_ResetCallback) for HAL_PCD_ResetCallback
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_IRQHandler) refers to stm32g0xx_ll_usb.o(.text.USB_SetDevAddress) for USB_SetDevAddress
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_IRQHandler) refers to stm32g0xx_ll_usb.o(.text.USB_ReadPMA) for USB_ReadPMA
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_IRQHandler) refers to usbd_conf.o(.text.HAL_PCD_SetupStageCallback) for HAL_PCD_SetupStageCallback
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_IRQHandler) refers to usbd_conf.o(.text.HAL_PCD_DataInStageCallback) for HAL_PCD_DataInStageCallback
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_IRQHandler) refers to usbd_conf.o(.text.HAL_PCD_DataOutStageCallback) for HAL_PCD_DataOutStageCallback
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_IRQHandler) refers to stm32g0xx_ll_usb.o(.text.USB_EPStartXfer) for USB_EPStartXfer
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_IRQHandler) refers to stm32g0xx_ll_usb.o(.text.USB_WritePMA) for USB_WritePMA
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_IRQHandler) refers to usbd_conf.o(.text.HAL_PCDEx_LPM_Callback) for HAL_PCDEx_LPM_Callback
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_IRQHandler) refers to usbd_conf.o(.text.HAL_PCD_ResumeCallback) for HAL_PCD_ResumeCallback
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_IRQHandler) refers to usbd_conf.o(.text.HAL_PCD_SuspendCallback) for HAL_PCD_SuspendCallback
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_IRQHandler) refers to usbd_conf.o(.text.HAL_PCD_SOFCallback) for HAL_PCD_SOFCallback
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_IRQHandler) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_IRQHandler) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_ResetCallback) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_ResetCallback) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_SetAddress) refers to stm32g0xx_ll_usb.o(.text.USB_SetDevAddress) for USB_SetDevAddress
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_SetAddress) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_SetAddress) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_ResumeCallback) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_ResumeCallback) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_SuspendCallback) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_SuspendCallback) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_SOFCallback) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_SOFCallback) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_DataOutStageCallback) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_DataOutStageCallback) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_DataInStageCallback) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_DataInStageCallback) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_SetupStageCallback) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_SetupStageCallback) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_ISOOUTIncompleteCallback) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_ISOOUTIncompleteCallback) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_ISOINIncompleteCallback) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_ISOINIncompleteCallback) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_ConnectCallback) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_ConnectCallback) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_DisconnectCallback) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_DisconnectCallback) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_DevConnect) refers to stm32g0xx_ll_usb.o(.text.USB_DevConnect) for USB_DevConnect
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_DevConnect) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_DevConnect) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_DevDisconnect) refers to stm32g0xx_ll_usb.o(.text.USB_DevDisconnect) for USB_DevDisconnect
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_DevDisconnect) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_DevDisconnect) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Open) refers to stm32g0xx_ll_usb.o(.text.USB_ActivateEndpoint) for USB_ActivateEndpoint
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_EP_Open) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Open) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Close) refers to stm32g0xx_ll_usb.o(.text.USB_DeactivateEndpoint) for USB_DeactivateEndpoint
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_EP_Close) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Close) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Receive) refers to stm32g0xx_ll_usb.o(.text.USB_EPStartXfer) for USB_EPStartXfer
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_EP_Receive) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Receive) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_EP_GetRxCount) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_GetRxCount) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Transmit) refers to stm32g0xx_ll_usb.o(.text.USB_EPStartXfer) for USB_EPStartXfer
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_EP_Transmit) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Transmit) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_SetStall) refers to stm32g0xx_ll_usb.o(.text.USB_EPSetStall) for USB_EPSetStall
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_EP_SetStall) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_SetStall) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_ClrStall) refers to stm32g0xx_ll_usb.o(.text.USB_EPClearStall) for USB_EPClearStall
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_EP_ClrStall) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_ClrStall) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Abort) refers to stm32g0xx_ll_usb.o(.text.USB_EPStopXfer) for USB_EPStopXfer
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_EP_Abort) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Abort) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Flush) refers to stm32g0xx_ll_usb.o(.text.USB_FlushRxFifo) for USB_FlushRxFifo
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Flush) refers to stm32g0xx_ll_usb.o(.text.USB_FlushTxFifo) for USB_FlushTxFifo
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_EP_Flush) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Flush) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_ActivateRemoteWakeup) refers to stm32g0xx_ll_usb.o(.text.USB_ActivateRemoteWakeup) for USB_ActivateRemoteWakeup
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_ActivateRemoteWakeup) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_ActivateRemoteWakeup) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.text.HAL_PCD_DeActivateRemoteWakeup) refers to stm32g0xx_ll_usb.o(.text.USB_DeActivateRemoteWakeup) for USB_DeActivateRemoteWakeup
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_DeActivateRemoteWakeup) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_DeActivateRemoteWakeup) for [Anonymous Symbol]
    stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_GetState) refers to stm32g0xx_hal_pcd.o(.text.HAL_PCD_GetState) for [Anonymous Symbol]
    stm32g0xx_hal_pcd_ex.o(.ARM.exidx.text.HAL_PCDEx_PMAConfig) refers to stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_PMAConfig) for [Anonymous Symbol]
    stm32g0xx_hal_pcd_ex.o(.ARM.exidx.text.HAL_PCDEx_ActivateBCD) refers to stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_ActivateBCD) for [Anonymous Symbol]
    stm32g0xx_hal_pcd_ex.o(.ARM.exidx.text.HAL_PCDEx_DeActivateBCD) refers to stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_DeActivateBCD) for [Anonymous Symbol]
    stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_BCD_VBUSDetect) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_BCD_VBUSDetect) refers to stm32g0xx_hal.o(.text.HAL_Delay) for HAL_Delay
    stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_BCD_VBUSDetect) refers to stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_BCD_Callback) for HAL_PCDEx_BCD_Callback
    stm32g0xx_hal_pcd_ex.o(.ARM.exidx.text.HAL_PCDEx_BCD_VBUSDetect) refers to stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_BCD_VBUSDetect) for [Anonymous Symbol]
    stm32g0xx_hal_pcd_ex.o(.ARM.exidx.text.HAL_PCDEx_BCD_Callback) refers to stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_BCD_Callback) for [Anonymous Symbol]
    stm32g0xx_hal_pcd_ex.o(.ARM.exidx.text.HAL_PCDEx_ActivateLPM) refers to stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_ActivateLPM) for [Anonymous Symbol]
    stm32g0xx_hal_pcd_ex.o(.ARM.exidx.text.HAL_PCDEx_DeActivateLPM) refers to stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_DeActivateLPM) for [Anonymous Symbol]
    stm32g0xx_hal_pcd_ex.o(.ARM.exidx.text.HAL_PCDEx_LPM_Callback) refers to stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_LPM_Callback) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_CoreInit) refers to stm32g0xx_ll_usb.o(.text.USB_CoreInit) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_EnableGlobalInt) refers to stm32g0xx_ll_usb.o(.text.USB_EnableGlobalInt) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_DisableGlobalInt) refers to stm32g0xx_ll_usb.o(.text.USB_DisableGlobalInt) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_SetCurrentMode) refers to stm32g0xx_ll_usb.o(.text.USB_SetCurrentMode) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_DevInit) refers to stm32g0xx_ll_usb.o(.text.USB_DevInit) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_FlushTxFifo) refers to stm32g0xx_ll_usb.o(.text.USB_FlushTxFifo) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_FlushRxFifo) refers to stm32g0xx_ll_usb.o(.text.USB_FlushRxFifo) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_ActivateEndpoint) refers to stm32g0xx_ll_usb.o(.text.USB_ActivateEndpoint) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_DeactivateEndpoint) refers to stm32g0xx_ll_usb.o(.text.USB_DeactivateEndpoint) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_EPStartXfer) refers to stm32g0xx_ll_usb.o(.text.USB_EPStartXfer) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_WritePMA) refers to stm32g0xx_ll_usb.o(.text.USB_WritePMA) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_EPSetStall) refers to stm32g0xx_ll_usb.o(.text.USB_EPSetStall) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_EPClearStall) refers to stm32g0xx_ll_usb.o(.text.USB_EPClearStall) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_EPStopXfer) refers to stm32g0xx_ll_usb.o(.text.USB_EPStopXfer) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_StopDevice) refers to stm32g0xx_ll_usb.o(.text.USB_StopDevice) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_SetDevAddress) refers to stm32g0xx_ll_usb.o(.text.USB_SetDevAddress) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_DevConnect) refers to stm32g0xx_ll_usb.o(.text.USB_DevConnect) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_DevDisconnect) refers to stm32g0xx_ll_usb.o(.text.USB_DevDisconnect) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_ReadInterrupts) refers to stm32g0xx_ll_usb.o(.text.USB_ReadInterrupts) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_ActivateRemoteWakeup) refers to stm32g0xx_ll_usb.o(.text.USB_ActivateRemoteWakeup) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_DeActivateRemoteWakeup) refers to stm32g0xx_ll_usb.o(.text.USB_DeActivateRemoteWakeup) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_ReadPMA) refers to stm32g0xx_ll_usb.o(.text.USB_ReadPMA) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_HostInit) refers to stm32g0xx_ll_usb.o(.text.USB_HostInit) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.text.USB_ResetPort) refers to stm32g0xx_hal.o(.text.HAL_Delay) for HAL_Delay
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_ResetPort) refers to stm32g0xx_ll_usb.o(.text.USB_ResetPort) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_GetHostSpeed) refers to stm32g0xx_ll_usb.o(.text.USB_GetHostSpeed) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_GetCurrentFrame) refers to stm32g0xx_ll_usb.o(.text.USB_GetCurrentFrame) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_HC_DoubleBuffer) refers to stm32g0xx_ll_usb.o(.text.USB_HC_DoubleBuffer) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_HC_Init) refers to stm32g0xx_ll_usb.o(.text.USB_HC_Init) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_HC_StartXfer) refers to stm32g0xx_ll_usb.o(.text.USB_HC_StartXfer) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_HC_IN_Halt) refers to stm32g0xx_ll_usb.o(.text.USB_HC_IN_Halt) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_HC_OUT_Halt) refers to stm32g0xx_ll_usb.o(.text.USB_HC_OUT_Halt) for [Anonymous Symbol]
    stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_StopHost) refers to stm32g0xx_ll_usb.o(.text.USB_StopHost) for [Anonymous Symbol]
    stm32g0xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to stm32g0xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32g0xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to system_stm32g0xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g0xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to stm32g0xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32g0xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to stm32g0xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32g0xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to system_stm32g0xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g0xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to stm32g0xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for [Anonymous Symbol]
    stm32g0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    stm32g0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32g0xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32g0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32g0xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    stm32g0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32g0xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32g0xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for [Anonymous Symbol]
    stm32g0xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for [Anonymous Symbol]
    stm32g0xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) refers to stm32g0xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) for [Anonymous Symbol]
    stm32g0xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) refers to system_stm32g0xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for [Anonymous Symbol]
    stm32g0xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32g0xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g0xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32g0xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for [Anonymous Symbol]
    stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig) for [Anonymous Symbol]
    stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig) for [Anonymous Symbol]
    stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_EnableCSS) for [Anonymous Symbol]
    stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableLSECSS) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_EnableLSECSS) for [Anonymous Symbol]
    stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableLSECSS) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_DisableLSECSS) for [Anonymous Symbol]
    stm32g0xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32g0xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_LSECSSCallback) for HAL_RCC_LSECSSCallback
    stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) for [Anonymous Symbol]
    stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for [Anonymous Symbol]
    stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_LSECSSCallback) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_LSECSSCallback) for [Anonymous Symbol]
    stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetResetSource) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_GetResetSource) for [Anonymous Symbol]
    stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for [Anonymous Symbol]
    stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig) refers to stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig) for [Anonymous Symbol]
    stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g0xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) for [Anonymous Symbol]
    stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnableLSCO) refers to stm32g0xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnableLSCO) refers to stm32g0xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnableLSCO) refers to stm32g0xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnableLSCO) refers to stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnableLSCO) for [Anonymous Symbol]
    stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisableLSCO) refers to stm32g0xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisableLSCO) refers to stm32g0xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisableLSCO) refers to stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisableLSCO) for [Anonymous Symbol]
    stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRSConfig) refers to stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRSConfig) for [Anonymous Symbol]
    stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRSSoftwareSynchronizationGenerate) refers to stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRSSoftwareSynchronizationGenerate) for [Anonymous Symbol]
    stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRSGetSynchronizationInfo) refers to stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRSGetSynchronizationInfo) for [Anonymous Symbol]
    stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRSWaitSynchronization) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRSWaitSynchronization) refers to stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRSWaitSynchronization) for [Anonymous Symbol]
    stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_IRQHandler) refers to stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_SyncOkCallback) for HAL_RCCEx_CRS_SyncOkCallback
    stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_IRQHandler) refers to stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_SyncWarnCallback) for HAL_RCCEx_CRS_SyncWarnCallback
    stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_IRQHandler) refers to stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_ExpectedSyncCallback) for HAL_RCCEx_CRS_ExpectedSyncCallback
    stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_IRQHandler) refers to stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_ErrorCallback) for HAL_RCCEx_CRS_ErrorCallback
    stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRS_IRQHandler) refers to stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_IRQHandler) for [Anonymous Symbol]
    stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRS_SyncOkCallback) refers to stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_SyncOkCallback) for [Anonymous Symbol]
    stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRS_SyncWarnCallback) refers to stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_SyncWarnCallback) for [Anonymous Symbol]
    stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRS_ExpectedSyncCallback) refers to stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_ExpectedSyncCallback) for [Anonymous Symbol]
    stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRS_ErrorCallback) refers to stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_ErrorCallback) for [Anonymous Symbol]
    stm32g0xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32g0xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32g0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program) refers to stm32g0xx_hal_flash.o(.text.HAL_FLASH_Program) for [Anonymous Symbol]
    stm32g0xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32g0xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32g0xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation) refers to stm32g0xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for [Anonymous Symbol]
    stm32g0xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32g0xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32g0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT) refers to stm32g0xx_hal_flash.o(.text.HAL_FLASH_Program_IT) for [Anonymous Symbol]
    stm32g0xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32g0xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32g0xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32g0xx_hal_flash_ex.o(.text.FLASH_PageErase) for FLASH_PageErase
    stm32g0xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32g0xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32g0xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32g0xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32g0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler) refers to stm32g0xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) for [Anonymous Symbol]
    stm32g0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback) refers to stm32g0xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for [Anonymous Symbol]
    stm32g0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback) refers to stm32g0xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for [Anonymous Symbol]
    stm32g0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock) refers to stm32g0xx_hal_flash.o(.text.HAL_FLASH_Unlock) for [Anonymous Symbol]
    stm32g0xx_hal_flash.o(.text.HAL_FLASH_Lock) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_flash.o(.text.HAL_FLASH_Lock) refers to stm32g0xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32g0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock) refers to stm32g0xx_hal_flash.o(.text.HAL_FLASH_Lock) for [Anonymous Symbol]
    stm32g0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock) refers to stm32g0xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock) for [Anonymous Symbol]
    stm32g0xx_hal_flash.o(.text.HAL_FLASH_OB_Lock) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_flash.o(.text.HAL_FLASH_OB_Lock) refers to stm32g0xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32g0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock) refers to stm32g0xx_hal_flash.o(.text.HAL_FLASH_OB_Lock) for [Anonymous Symbol]
    stm32g0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch) refers to stm32g0xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) for [Anonymous Symbol]
    stm32g0xx_hal_flash.o(.text.HAL_FLASH_GetError) refers to stm32g0xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32g0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError) refers to stm32g0xx_hal_flash.o(.text.HAL_FLASH_GetError) for [Anonymous Symbol]
    stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32g0xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32g0xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32g0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase) refers to stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) for [Anonymous Symbol]
    stm32g0xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_PageErase) refers to stm32g0xx_hal_flash_ex.o(.text.FLASH_PageErase) for [Anonymous Symbol]
    stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) refers to stm32g0xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) refers to stm32g0xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32g0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT) refers to stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) for [Anonymous Symbol]
    stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32g0xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32g0xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32g0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram) refers to stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) for [Anonymous Symbol]
    stm32g0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig) refers to stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) for [Anonymous Symbol]
    stm32g0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_EnableDebugger) refers to stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_EnableDebugger) for [Anonymous Symbol]
    stm32g0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_DisableDebugger) refers to stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_DisableDebugger) for [Anonymous Symbol]
    stm32g0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_FlashEmptyCheck) refers to stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_FlashEmptyCheck) for [Anonymous Symbol]
    stm32g0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_ForceFlashEmpty) refers to stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_ForceFlashEmpty) for [Anonymous Symbol]
    stm32g0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_EnableSecMemProtection) refers to stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_EnableSecMemProtection) for [Anonymous Symbol]
    stm32g0xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches) refers to stm32g0xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for [Anonymous Symbol]
    stm32g0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init) refers to stm32g0xx_hal_gpio.o(.text.HAL_GPIO_Init) for [Anonymous Symbol]
    stm32g0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit) refers to stm32g0xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for [Anonymous Symbol]
    stm32g0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin) refers to stm32g0xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for [Anonymous Symbol]
    stm32g0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin) refers to stm32g0xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for [Anonymous Symbol]
    stm32g0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin) refers to stm32g0xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for [Anonymous Symbol]
    stm32g0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin) refers to stm32g0xx_hal_gpio.o(.text.HAL_GPIO_LockPin) for [Anonymous Symbol]
    stm32g0xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32g0xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Rising_Callback) for HAL_GPIO_EXTI_Rising_Callback
    stm32g0xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32g0xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Falling_Callback) for HAL_GPIO_EXTI_Falling_Callback
    stm32g0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32g0xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32g0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Rising_Callback) refers to stm32g0xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Rising_Callback) for [Anonymous Symbol]
    stm32g0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Falling_Callback) refers to stm32g0xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Falling_Callback) for [Anonymous Symbol]
    stm32g0xx_hal_dma.o(.text.HAL_DMA_Init) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Init) for [Anonymous Symbol]
    stm32g0xx_hal_dma.o(.text.HAL_DMA_DeInit) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_DeInit) for [Anonymous Symbol]
    stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Start) for [Anonymous Symbol]
    stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for [Anonymous Symbol]
    stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Abort) for [Anonymous Symbol]
    stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for [Anonymous Symbol]
    stm32g0xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) for [Anonymous Symbol]
    stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for [Anonymous Symbol]
    stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_RegisterCallback) for [Anonymous Symbol]
    stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback) for [Anonymous Symbol]
    stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_GetState) for [Anonymous Symbol]
    stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_GetError) for [Anonymous Symbol]
    stm32g0xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ConfigMuxSync) refers to stm32g0xx_hal_dma_ex.o(.text.HAL_DMAEx_ConfigMuxSync) for [Anonymous Symbol]
    stm32g0xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ConfigMuxRequestGenerator) refers to stm32g0xx_hal_dma_ex.o(.text.HAL_DMAEx_ConfigMuxRequestGenerator) for [Anonymous Symbol]
    stm32g0xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_EnableMuxRequestGenerator) refers to stm32g0xx_hal_dma_ex.o(.text.HAL_DMAEx_EnableMuxRequestGenerator) for [Anonymous Symbol]
    stm32g0xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_DisableMuxRequestGenerator) refers to stm32g0xx_hal_dma_ex.o(.text.HAL_DMAEx_DisableMuxRequestGenerator) for [Anonymous Symbol]
    stm32g0xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MUX_IRQHandler) refers to stm32g0xx_hal_dma_ex.o(.text.HAL_DMAEx_MUX_IRQHandler) for [Anonymous Symbol]
    stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit) refers to stm32g0xx_hal_pwr.o(.text.HAL_PWR_DeInit) for [Anonymous Symbol]
    stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess) refers to stm32g0xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess) for [Anonymous Symbol]
    stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess) refers to stm32g0xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess) for [Anonymous Symbol]
    stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin) refers to stm32g0xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin) for [Anonymous Symbol]
    stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin) refers to stm32g0xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin) for [Anonymous Symbol]
    stm32g0xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableLowPowerRunMode) for HAL_PWREx_EnableLowPowerRunMode
    stm32g0xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableLowPowerRunMode) for HAL_PWREx_DisableLowPowerRunMode
    stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode) refers to stm32g0xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode) for [Anonymous Symbol]
    stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode) refers to stm32g0xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode) for [Anonymous Symbol]
    stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode) refers to stm32g0xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode) for [Anonymous Symbol]
    stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit) refers to stm32g0xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit) for [Anonymous Symbol]
    stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit) refers to stm32g0xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit) for [Anonymous Symbol]
    stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend) refers to stm32g0xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend) for [Anonymous Symbol]
    stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend) refers to stm32g0xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBatteryCharging) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBatteryCharging) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBatteryCharging) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBatteryCharging) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnablePORMonitorSampling) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnablePORMonitorSampling) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisablePORMonitorSampling) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisablePORMonitorSampling) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ConfigPVD) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_ConfigPVD) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnablePVD) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnablePVD) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisablePVD) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisablePVD) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableVddUSB) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableVddUSB) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableVddUSB) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableVddUSB) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableVddIO2) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableVddIO2) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableVddIO2) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableVddIO2) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnablePVMUSB) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnablePVMUSB) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisablePVMUSB) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisablePVMUSB) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ConfigPVM) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_ConfigPVM) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableInternalWakeUpLine) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableInternalWakeUpLine) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableInternalWakeUpLine) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableInternalWakeUpLine) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableGPIOPullUp) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableGPIOPullUp) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableGPIOPullUp) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableGPIOPullUp) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableGPIOPullDown) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableGPIOPullDown) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableGPIOPullDown) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableGPIOPullDown) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnablePullUpPullDownConfig) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnablePullUpPullDownConfig) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisablePullUpPullDownConfig) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisablePullUpPullDownConfig) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableSRAMRetention) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableSRAMRetention) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableSRAMRetention) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableSRAMRetention) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) refers to system_stm32g0xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableLowPowerRunMode) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableLowPowerRunMode) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableLowPowerRunMode) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableLowPowerRunMode) refers to system_stm32g0xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableLowPowerRunMode) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableLowPowerRunMode) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnterSHUTDOWNMode) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnterSHUTDOWNMode) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_PVD_PVM_Rising_Callback) for HAL_PWREx_PVD_PVM_Rising_Callback
    stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_PVD_PVM_Falling_Callback) for HAL_PWREx_PVD_PVM_Falling_Callback
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_PVD_PVM_IRQHandler) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_PVD_PVM_Rising_Callback) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_PVD_PVM_Rising_Callback) for [Anonymous Symbol]
    stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_PVD_PVM_Falling_Callback) refers to stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_PVD_PVM_Falling_Callback) for [Anonymous Symbol]
    stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority) refers to stm32g0xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for [Anonymous Symbol]
    stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ) refers to stm32g0xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for [Anonymous Symbol]
    stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ) refers to stm32g0xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for [Anonymous Symbol]
    stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset) refers to stm32g0xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) for [Anonymous Symbol]
    stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config) refers to stm32g0xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for [Anonymous Symbol]
    stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority) refers to stm32g0xx_hal_cortex.o(.text.HAL_NVIC_GetPriority) for [Anonymous Symbol]
    stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ) refers to stm32g0xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ) for [Anonymous Symbol]
    stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ) refers to stm32g0xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ) for [Anonymous Symbol]
    stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ) refers to stm32g0xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig) refers to stm32g0xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig) for [Anonymous Symbol]
    stm32g0xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) refers to stm32g0xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler) refers to stm32g0xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) for [Anonymous Symbol]
    stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback) refers to stm32g0xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for [Anonymous Symbol]
    stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable) refers to stm32g0xx_hal_cortex.o(.text.HAL_MPU_Enable) for [Anonymous Symbol]
    stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable) refers to stm32g0xx_hal_cortex.o(.text.HAL_MPU_Disable) for [Anonymous Symbol]
    stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion) refers to stm32g0xx_hal_cortex.o(.text.HAL_MPU_EnableRegion) for [Anonymous Symbol]
    stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion) refers to stm32g0xx_hal_cortex.o(.text.HAL_MPU_DisableRegion) for [Anonymous Symbol]
    stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion) refers to stm32g0xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion) for [Anonymous Symbol]
    stm32g0xx_hal.o(.text.HAL_Init) refers to stm32g0xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32g0xx_hal.o(.text.HAL_Init) refers to stm32g0xx_hal_msp.o(.text.HAL_MspInit) for HAL_MspInit
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_Init) refers to stm32g0xx_hal.o(.text.HAL_Init) for [Anonymous Symbol]
    stm32g0xx_hal.o(.text.HAL_InitTick) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    stm32g0xx_hal.o(.text.HAL_InitTick) refers to stm32g0xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32g0xx_hal.o(.text.HAL_InitTick) refers to stm32g0xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32g0xx_hal.o(.text.HAL_InitTick) refers to stm32g0xx_hal.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    stm32g0xx_hal.o(.text.HAL_InitTick) refers to system_stm32g0xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_InitTick) refers to stm32g0xx_hal.o(.text.HAL_InitTick) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_MspInit) refers to stm32g0xx_hal.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32g0xx_hal.o(.text.HAL_DeInit) refers to stm32g0xx_hal.o(.text.HAL_MspDeInit) for HAL_MspDeInit
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_DeInit) refers to stm32g0xx_hal.o(.text.HAL_DeInit) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_MspDeInit) refers to stm32g0xx_hal.o(.text.HAL_MspDeInit) for [Anonymous Symbol]
    stm32g0xx_hal.o(.text.HAL_IncTick) refers to stm32g0xx_hal.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    stm32g0xx_hal.o(.text.HAL_IncTick) refers to stm32g0xx_hal.o(.bss.uwTick) for uwTick
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_IncTick) refers to stm32g0xx_hal.o(.text.HAL_IncTick) for [Anonymous Symbol]
    stm32g0xx_hal.o(.text.HAL_GetTick) refers to stm32g0xx_hal.o(.bss.uwTick) for uwTick
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_GetTick) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for [Anonymous Symbol]
    stm32g0xx_hal.o(.text.HAL_GetTickPrio) refers to stm32g0xx_hal.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio) refers to stm32g0xx_hal.o(.text.HAL_GetTickPrio) for [Anonymous Symbol]
    stm32g0xx_hal.o(.text.HAL_SetTickFreq) refers to stm32g0xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32g0xx_hal.o(.text.HAL_SetTickFreq) refers to stm32g0xx_hal.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq) refers to stm32g0xx_hal.o(.text.HAL_SetTickFreq) for [Anonymous Symbol]
    stm32g0xx_hal.o(.text.HAL_GetTickFreq) refers to stm32g0xx_hal.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq) refers to stm32g0xx_hal.o(.text.HAL_GetTickFreq) for [Anonymous Symbol]
    stm32g0xx_hal.o(.text.HAL_Delay) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal.o(.text.HAL_Delay) refers to stm32g0xx_hal.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_Delay) refers to stm32g0xx_hal.o(.text.HAL_Delay) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_SuspendTick) refers to stm32g0xx_hal.o(.text.HAL_SuspendTick) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_ResumeTick) refers to stm32g0xx_hal.o(.text.HAL_ResumeTick) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion) refers to stm32g0xx_hal.o(.text.HAL_GetHalVersion) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_GetREVID) refers to stm32g0xx_hal.o(.text.HAL_GetREVID) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_GetDEVID) refers to stm32g0xx_hal.o(.text.HAL_GetDEVID) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0) refers to stm32g0xx_hal.o(.text.HAL_GetUIDw0) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1) refers to stm32g0xx_hal.o(.text.HAL_GetUIDw1) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2) refers to stm32g0xx_hal.o(.text.HAL_GetUIDw2) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode) refers to stm32g0xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode) refers to stm32g0xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode) refers to stm32g0xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode) refers to stm32g0xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_VREFBUF_VoltageScalingConfig) refers to stm32g0xx_hal.o(.text.HAL_SYSCFG_VREFBUF_VoltageScalingConfig) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_VREFBUF_HighImpedanceConfig) refers to stm32g0xx_hal.o(.text.HAL_SYSCFG_VREFBUF_HighImpedanceConfig) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_VREFBUF_TrimmingConfig) refers to stm32g0xx_hal.o(.text.HAL_SYSCFG_VREFBUF_TrimmingConfig) for [Anonymous Symbol]
    stm32g0xx_hal.o(.text.HAL_SYSCFG_EnableVREFBUF) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_EnableVREFBUF) refers to stm32g0xx_hal.o(.text.HAL_SYSCFG_EnableVREFBUF) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_DisableVREFBUF) refers to stm32g0xx_hal.o(.text.HAL_SYSCFG_DisableVREFBUF) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_EnableIOAnalogSwitchBooster) refers to stm32g0xx_hal.o(.text.HAL_SYSCFG_EnableIOAnalogSwitchBooster) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_DisableIOAnalogSwitchBooster) refers to stm32g0xx_hal.o(.text.HAL_SYSCFG_DisableIOAnalogSwitchBooster) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_EnableRemap) refers to stm32g0xx_hal.o(.text.HAL_SYSCFG_EnableRemap) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_DisableRemap) refers to stm32g0xx_hal.o(.text.HAL_SYSCFG_DisableRemap) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_EnableClampingDiode) refers to stm32g0xx_hal.o(.text.HAL_SYSCFG_EnableClampingDiode) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_DisableClampingDiode) refers to stm32g0xx_hal.o(.text.HAL_SYSCFG_DisableClampingDiode) for [Anonymous Symbol]
    stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_StrobeDBattpinsConfig) refers to stm32g0xx_hal.o(.text.HAL_SYSCFG_StrobeDBattpinsConfig) for [Anonymous Symbol]
    stm32g0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine) refers to stm32g0xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine) for [Anonymous Symbol]
    stm32g0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine) refers to stm32g0xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine) for [Anonymous Symbol]
    stm32g0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine) refers to stm32g0xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine) for [Anonymous Symbol]
    stm32g0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback) refers to stm32g0xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback) for [Anonymous Symbol]
    stm32g0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle) refers to stm32g0xx_hal_exti.o(.text.HAL_EXTI_GetHandle) for [Anonymous Symbol]
    stm32g0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler) refers to stm32g0xx_hal_exti.o(.text.HAL_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32g0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending) refers to stm32g0xx_hal_exti.o(.text.HAL_EXTI_GetPending) for [Anonymous Symbol]
    stm32g0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending) refers to stm32g0xx_hal_exti.o(.text.HAL_EXTI_ClearPending) for [Anonymous Symbol]
    stm32g0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI) refers to stm32g0xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Init) refers to spi.o(.text.HAL_SPI_MspInit) for HAL_SPI_MspInit
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Init) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_Init) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_MspInit) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_MspInit) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.HAL_SPI_DeInit) refers to spi.o(.text.HAL_SPI_MspDeInit) for HAL_SPI_MspDeInit
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DeInit) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_DeInit) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_MspDeInit) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_MspDeInit) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Transmit) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Transmit) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Transmit) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Transmit) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_Transmit) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Receive) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Receive) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Receive) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Receive) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Receive) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_Receive) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.HAL_SPI_TransmitReceive) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_spi.o(.text.HAL_SPI_TransmitReceive) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.HAL_SPI_TransmitReceive) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TransmitReceive) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_TransmitReceive) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Transmit_IT) refers to stm32g0xx_hal_spi.o(.text.SPI_TxISR_16BIT) for SPI_TxISR_16BIT
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Transmit_IT) refers to stm32g0xx_hal_spi.o(.text.SPI_TxISR_8BIT) for SPI_TxISR_8BIT
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Transmit_IT) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_Transmit_IT) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_TxISR_16BIT) refers to stm32g0xx_hal_spi.o(.text.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_TxISR_16BIT) refers to stm32g0xx_hal_spi.o(.text.SPI_TxISR_16BIT) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_TxISR_8BIT) refers to stm32g0xx_hal_spi.o(.text.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_TxISR_8BIT) refers to stm32g0xx_hal_spi.o(.text.SPI_TxISR_8BIT) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Receive_IT) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_IT) for HAL_SPI_TransmitReceive_IT
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Receive_IT) refers to stm32g0xx_hal_spi.o(.text.SPI_RxISR_16BIT) for SPI_RxISR_16BIT
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Receive_IT) refers to stm32g0xx_hal_spi.o(.text.SPI_RxISR_8BIT) for SPI_RxISR_8BIT
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Receive_IT) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_Receive_IT) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_IT) refers to stm32g0xx_hal_spi.o(.text.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32g0xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_IT) refers to stm32g0xx_hal_spi.o(.text.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32g0xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_IT) refers to stm32g0xx_hal_spi.o(.text.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32g0xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_IT) refers to stm32g0xx_hal_spi.o(.text.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TransmitReceive_IT) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_IT) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_RxISR_16BIT) refers to stm32g0xx_hal_spi.o(.text.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_RxISR_16BIT) refers to stm32g0xx_hal_spi.o(.text.SPI_RxISR_16BIT) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_RxISR_8BIT) refers to stm32g0xx_hal_spi.o(.text.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_RxISR_8BIT) refers to stm32g0xx_hal_spi.o(.text.SPI_RxISR_8BIT) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_2linesRxISR_16BIT) refers to stm32g0xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_2linesRxISR_16BIT) refers to stm32g0xx_hal_spi.o(.text.SPI_2linesRxISR_16BIT) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_2linesTxISR_16BIT) refers to stm32g0xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_2linesTxISR_16BIT) refers to stm32g0xx_hal_spi.o(.text.SPI_2linesTxISR_16BIT) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_2linesRxISR_8BIT) refers to stm32g0xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_2linesRxISR_8BIT) refers to stm32g0xx_hal_spi.o(.text.SPI_2linesRxISR_8BIT) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_2linesTxISR_8BIT) refers to stm32g0xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_2linesTxISR_8BIT) refers to stm32g0xx_hal_spi.o(.text.SPI_2linesTxISR_8BIT) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Transmit_DMA) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Transmit_DMA) refers to stm32g0xx_hal_spi.o(.text.SPI_DMAHalfTransmitCplt) for SPI_DMAHalfTransmitCplt
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Transmit_DMA) refers to stm32g0xx_hal_spi.o(.text.SPI_DMAError) for SPI_DMAError
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Transmit_DMA) refers to stm32g0xx_hal_spi.o(.text.SPI_DMATransmitCplt) for SPI_DMATransmitCplt
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Transmit_DMA) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_Transmit_DMA) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_DMAHalfTransmitCplt) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_TxHalfCpltCallback) for HAL_SPI_TxHalfCpltCallback
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_DMAHalfTransmitCplt) refers to stm32g0xx_hal_spi.o(.text.SPI_DMAHalfTransmitCplt) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_DMATransmitCplt) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_spi.o(.text.SPI_DMATransmitCplt) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.SPI_DMATransmitCplt) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.SPI_DMATransmitCplt) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32g0xx_hal_spi.o(.text.SPI_DMATransmitCplt) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_DMATransmitCplt) refers to stm32g0xx_hal_spi.o(.text.SPI_DMATransmitCplt) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_DMAError) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_DMAError) refers to stm32g0xx_hal_spi.o(.text.SPI_DMAError) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Receive_DMA) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) for HAL_SPI_TransmitReceive_DMA
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Receive_DMA) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Receive_DMA) refers to stm32g0xx_hal_spi.o(.text.SPI_DMAError) for SPI_DMAError
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Receive_DMA) refers to stm32g0xx_hal_spi.o(.text.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Receive_DMA) refers to stm32g0xx_hal_spi.o(.text.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Receive_DMA) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_Receive_DMA) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) refers to stm32g0xx_hal_spi.o(.text.SPI_DMAError) for SPI_DMAError
    stm32g0xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) refers to stm32g0xx_hal_spi.o(.text.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32g0xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) refers to stm32g0xx_hal_spi.o(.text.SPI_DMAHalfTransmitReceiveCplt) for SPI_DMAHalfTransmitReceiveCplt
    stm32g0xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) refers to stm32g0xx_hal_spi.o(.text.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32g0xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) refers to stm32g0xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt) for SPI_DMATransmitReceiveCplt
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TransmitReceive_DMA) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_DMAHalfReceiveCplt) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_RxHalfCpltCallback) for HAL_SPI_RxHalfCpltCallback
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_DMAHalfReceiveCplt) refers to stm32g0xx_hal_spi.o(.text.SPI_DMAHalfReceiveCplt) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_DMAReceiveCplt) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_spi.o(.text.SPI_DMAReceiveCplt) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.SPI_DMAReceiveCplt) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.SPI_DMAReceiveCplt) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32g0xx_hal_spi.o(.text.SPI_DMAReceiveCplt) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_DMAReceiveCplt) refers to stm32g0xx_hal_spi.o(.text.SPI_DMAReceiveCplt) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_DMAHalfTransmitReceiveCplt) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_TxRxHalfCpltCallback) for HAL_SPI_TxRxHalfCpltCallback
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_DMAHalfTransmitReceiveCplt) refers to stm32g0xx_hal_spi.o(.text.SPI_DMAHalfTransmitReceiveCplt) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32g0xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_DMATransmitReceiveCplt) refers to stm32g0xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Abort) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Abort) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Abort) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Abort) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Abort) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Abort) refers to system_stm32g0xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Abort) refers to stm32g0xx_hal_spi.o(.text.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Abort) refers to stm32g0xx_hal_spi.o(.text.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Abort) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_Abort) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_AbortTx_ISR) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    stm32g0xx_hal_spi.o(.text.SPI_AbortTx_ISR) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_spi.o(.text.SPI_AbortTx_ISR) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.SPI_AbortTx_ISR) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.SPI_AbortTx_ISR) refers to system_stm32g0xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_AbortTx_ISR) refers to stm32g0xx_hal_spi.o(.text.SPI_AbortTx_ISR) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_AbortRx_ISR) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    stm32g0xx_hal_spi.o(.text.SPI_AbortRx_ISR) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_spi.o(.text.SPI_AbortRx_ISR) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.SPI_AbortRx_ISR) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.SPI_AbortRx_ISR) refers to system_stm32g0xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_AbortRx_ISR) refers to stm32g0xx_hal_spi.o(.text.SPI_AbortRx_ISR) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_WaitFifoStateUntilTimeout) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_spi.o(.text.SPI_WaitFifoStateUntilTimeout) refers to system_stm32g0xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_WaitFifoStateUntilTimeout) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFifoStateUntilTimeout) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) refers to system_stm32g0xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_WaitFlagStateUntilTimeout) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Abort_IT) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Abort_IT) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Abort_IT) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Abort_IT) refers to system_stm32g0xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Abort_IT) refers to stm32g0xx_hal_spi.o(.text.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Abort_IT) refers to stm32g0xx_hal_spi.o(.text.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Abort_IT) refers to stm32g0xx_hal_spi.o(.text.SPI_DMATxAbortCallback) for SPI_DMATxAbortCallback
    stm32g0xx_hal_spi.o(.text.HAL_SPI_Abort_IT) refers to stm32g0xx_hal_spi.o(.text.SPI_DMARxAbortCallback) for SPI_DMARxAbortCallback
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Abort_IT) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_Abort_IT) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_DMATxAbortCallback) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_spi.o(.text.SPI_DMATxAbortCallback) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.SPI_DMATxAbortCallback) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.SPI_DMATxAbortCallback) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_DMATxAbortCallback) refers to stm32g0xx_hal_spi.o(.text.SPI_DMATxAbortCallback) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_DMARxAbortCallback) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_spi.o(.text.SPI_DMARxAbortCallback) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.SPI_DMARxAbortCallback) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.SPI_DMARxAbortCallback) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_DMARxAbortCallback) refers to stm32g0xx_hal_spi.o(.text.SPI_DMARxAbortCallback) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_AbortCpltCallback) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_AbortCpltCallback) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DMAPause) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_DMAPause) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DMAResume) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_DMAResume) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.HAL_SPI_DMAStop) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DMAStop) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_DMAStop) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.HAL_SPI_IRQHandler) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_spi.o(.text.HAL_SPI_IRQHandler) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32g0xx_hal_spi.o(.text.HAL_SPI_IRQHandler) refers to stm32g0xx_hal_spi.o(.text.SPI_DMAAbortOnError) for SPI_DMAAbortOnError
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_IRQHandler) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_IRQHandler) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_DMAAbortOnError) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_DMAAbortOnError) refers to stm32g0xx_hal_spi.o(.text.SPI_DMAAbortOnError) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_ErrorCallback) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxCpltCallback) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_TxCpltCallback) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_RxCpltCallback) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_RxCpltCallback) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxRxCpltCallback) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_TxRxCpltCallback) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxHalfCpltCallback) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_TxHalfCpltCallback) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_RxHalfCpltCallback) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_RxHalfCpltCallback) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxRxHalfCpltCallback) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_TxRxHalfCpltCallback) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_GetState) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_GetState) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_GetError) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_GetError) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32g0xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32g0xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_CloseRxTx_ISR) refers to stm32g0xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_CloseRx_ISR) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_spi.o(.text.SPI_CloseRx_ISR) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.SPI_CloseRx_ISR) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.SPI_CloseRx_ISR) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32g0xx_hal_spi.o(.text.SPI_CloseRx_ISR) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_CloseRx_ISR) refers to stm32g0xx_hal_spi.o(.text.SPI_CloseRx_ISR) for [Anonymous Symbol]
    stm32g0xx_hal_spi.o(.text.SPI_CloseTx_ISR) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_spi.o(.text.SPI_CloseTx_ISR) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.SPI_CloseTx_ISR) refers to stm32g0xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32g0xx_hal_spi.o(.text.SPI_CloseTx_ISR) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32g0xx_hal_spi.o(.text.SPI_CloseTx_ISR) refers to stm32g0xx_hal_spi.o(.text.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_CloseTx_ISR) refers to stm32g0xx_hal_spi.o(.text.SPI_CloseTx_ISR) for [Anonymous Symbol]
    stm32g0xx_hal_spi_ex.o(.ARM.exidx.text.HAL_SPIEx_FlushRxFifo) refers to stm32g0xx_hal_spi_ex.o(.text.HAL_SPIEx_FlushRxFifo) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Init) refers to tim.o(.text.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Init) refers to stm32g0xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Init) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_MspInit) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig) refers to stm32g0xx_hal_tim.o(.text.TIM_Base_SetConfig) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) refers to tim.o(.text.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Start) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Stop) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt) refers to stm32g0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32g0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.TIM_DMAError) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError) refers to stm32g0xx_hal_tim.o(.text.TIM_DMAError) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Init) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Init) refers to stm32g0xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Init) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Start) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd) refers to stm32g0xx_hal_tim.o(.text.TIM_CCxChannelCmd) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Stop) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) refers to stm32g0xx_hal_tim.o(.rodata.cst16) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) refers to stm32g0xx_hal_tim.o(.rodata.cst16) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt) refers to stm32g0xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt) refers to stm32g0xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) refers to stm32g0xx_hal_tim.o(.rodata.cst16) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Init) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Init) refers to stm32g0xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Init) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Start) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Stop) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) refers to stm32g0xx_hal_tim.o(.rodata.cst16) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) refers to stm32g0xx_hal_tim.o(.rodata.cst16) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) refers to stm32g0xx_hal_tim.o(.rodata.cst16) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Init) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Init) refers to stm32g0xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Init) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_MspInit) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Start) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Stop) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) refers to stm32g0xx_hal_tim.o(.rodata.cst16) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) refers to stm32g0xx_hal_tim.o(.rodata.cst16) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.TIM_DMACaptureCplt) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt) refers to stm32g0xx_hal_tim.o(.text.TIM_DMACaptureCplt) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt) refers to stm32g0xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) refers to stm32g0xx_hal_tim.o(.rodata.cst16) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) refers to stm32g0xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) refers to stm32g0xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_Start) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32g0xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32g0xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32g0xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32g0xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32g0xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_Break2Callback) for HAL_TIMEx_Break2Callback
    stm32g0xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32g0xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_IRQHandler) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig) refers to stm32g0xx_hal_tim.o(.text.TIM_OC2_SetConfig) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig) refers to stm32g0xx_hal_tim.o(.text.TIM_TI1_SetConfig) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g0xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g0xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g0xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g0xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g0xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.TIM_DMATriggerCplt) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt) refers to stm32g0xx_hal_tim.o(.text.TIM_DMATriggerCplt) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt) refers to stm32g0xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g0xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g0xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g0xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g0xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g0xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_GenerateEvent) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig) refers to stm32g0xx_hal_tim.o(.text.TIM_ETR_SetConfig) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) refers to stm32g0xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig) refers to stm32g0xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32g0xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_GetState) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_GetState) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_GetState) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_GetState) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_GetChannelState) for [Anonymous Symbol]
    stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurstState) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32g0xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32g0xx_hal_tim.o(.text.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32g0xx_hal_tim.o(.text.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) refers to stm32g0xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) refers to stm32g0xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32g0xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32g0xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32g0xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32g0xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32g0xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt) refers to stm32g0xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN) refers to stm32g0xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g0xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g0xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g0xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32g0xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) refers to stm32g0xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32g0xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32g0xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32g0xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32g0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32g0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32g0xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt) refers to stm32g0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt) refers to stm32g0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakInput) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakInput) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_TISelection) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_TISelection) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GroupChannel5) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_GroupChannel5) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_DisarmBreakInput) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_DisarmBreakInput) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_ReArmBreakInput) refers to stm32g0xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ReArmBreakInput) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_ReArmBreakInput) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_Break2Callback) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_Break2Callback) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState) for [Anonymous Symbol]
    stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState) refers to stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState) for [Anonymous Symbol]
    system_stm32g0xx.o(.ARM.exidx.text.SystemInit) refers to system_stm32g0xx.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32g0xx.o(.text.SystemCoreClockUpdate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    system_stm32g0xx.o(.text.SystemCoreClockUpdate) refers to system_stm32g0xx.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32g0xx.o(.text.SystemCoreClockUpdate) refers to system_stm32g0xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32g0xx.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32g0xx.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    usbd_core.o(.text.USBD_Init) refers to usbd_conf.o(.text.USBD_LL_Init) for USBD_LL_Init
    usbd_core.o(.ARM.exidx.text.USBD_Init) refers to usbd_core.o(.text.USBD_Init) for [Anonymous Symbol]
    usbd_core.o(.text.USBD_DeInit) refers to usbd_conf.o(.text.USBD_LL_Stop) for USBD_LL_Stop
    usbd_core.o(.text.USBD_DeInit) refers to usbd_conf.o(.text.USBD_LL_DeInit) for USBD_LL_DeInit
    usbd_core.o(.ARM.exidx.text.USBD_DeInit) refers to usbd_core.o(.text.USBD_DeInit) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.USBD_RegisterClass) refers to usbd_core.o(.text.USBD_RegisterClass) for [Anonymous Symbol]
    usbd_core.o(.text.USBD_Start) refers to usbd_conf.o(.text.USBD_LL_Start) for USBD_LL_Start
    usbd_core.o(.ARM.exidx.text.USBD_Start) refers to usbd_core.o(.text.USBD_Start) for [Anonymous Symbol]
    usbd_core.o(.text.USBD_Stop) refers to usbd_conf.o(.text.USBD_LL_Stop) for USBD_LL_Stop
    usbd_core.o(.ARM.exidx.text.USBD_Stop) refers to usbd_core.o(.text.USBD_Stop) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.USBD_RunTestMode) refers to usbd_core.o(.text.USBD_RunTestMode) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.USBD_SetClassConfig) refers to usbd_core.o(.text.USBD_SetClassConfig) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.USBD_ClrClassConfig) refers to usbd_core.o(.text.USBD_ClrClassConfig) for [Anonymous Symbol]
    usbd_core.o(.text.USBD_LL_SetupStage) refers to usbd_ctlreq.o(.text.USBD_ParseSetupRequest) for USBD_ParseSetupRequest
    usbd_core.o(.text.USBD_LL_SetupStage) refers to usbd_ctlreq.o(.text.USBD_StdDevReq) for USBD_StdDevReq
    usbd_core.o(.text.USBD_LL_SetupStage) refers to usbd_ctlreq.o(.text.USBD_StdItfReq) for USBD_StdItfReq
    usbd_core.o(.text.USBD_LL_SetupStage) refers to usbd_ctlreq.o(.text.USBD_StdEPReq) for USBD_StdEPReq
    usbd_core.o(.text.USBD_LL_SetupStage) refers to usbd_conf.o(.text.USBD_LL_StallEP) for USBD_LL_StallEP
    usbd_core.o(.ARM.exidx.text.USBD_LL_SetupStage) refers to usbd_core.o(.text.USBD_LL_SetupStage) for [Anonymous Symbol]
    usbd_core.o(.text.USBD_LL_DataOutStage) refers to usbd_ioreq.o(.text.USBD_CtlContinueRx) for USBD_CtlContinueRx
    usbd_core.o(.text.USBD_LL_DataOutStage) refers to usbd_ioreq.o(.text.USBD_CtlSendStatus) for USBD_CtlSendStatus
    usbd_core.o(.ARM.exidx.text.USBD_LL_DataOutStage) refers to usbd_core.o(.text.USBD_LL_DataOutStage) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.USBD_CoreFindIF) refers to usbd_core.o(.text.USBD_CoreFindIF) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.USBD_CoreFindEP) refers to usbd_core.o(.text.USBD_CoreFindEP) for [Anonymous Symbol]
    usbd_core.o(.text.USBD_LL_DataInStage) refers to usbd_ioreq.o(.text.USBD_CtlContinueSendData) for USBD_CtlContinueSendData
    usbd_core.o(.text.USBD_LL_DataInStage) refers to usbd_conf.o(.text.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_core.o(.text.USBD_LL_DataInStage) refers to usbd_conf.o(.text.USBD_LL_StallEP) for USBD_LL_StallEP
    usbd_core.o(.text.USBD_LL_DataInStage) refers to usbd_ioreq.o(.text.USBD_CtlReceiveStatus) for USBD_CtlReceiveStatus
    usbd_core.o(.ARM.exidx.text.USBD_LL_DataInStage) refers to usbd_core.o(.text.USBD_LL_DataInStage) for [Anonymous Symbol]
    usbd_core.o(.text.USBD_LL_Reset) refers to usbd_conf.o(.text.USBD_LL_OpenEP) for USBD_LL_OpenEP
    usbd_core.o(.ARM.exidx.text.USBD_LL_Reset) refers to usbd_core.o(.text.USBD_LL_Reset) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.USBD_LL_SetSpeed) refers to usbd_core.o(.text.USBD_LL_SetSpeed) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.USBD_LL_Suspend) refers to usbd_core.o(.text.USBD_LL_Suspend) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.USBD_LL_Resume) refers to usbd_core.o(.text.USBD_LL_Resume) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.USBD_LL_SOF) refers to usbd_core.o(.text.USBD_LL_SOF) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.USBD_LL_IsoINIncomplete) refers to usbd_core.o(.text.USBD_LL_IsoINIncomplete) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.USBD_LL_IsoOUTIncomplete) refers to usbd_core.o(.text.USBD_LL_IsoOUTIncomplete) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.USBD_LL_DevConnected) refers to usbd_core.o(.text.USBD_LL_DevConnected) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.USBD_LL_DevDisconnected) refers to usbd_core.o(.text.USBD_LL_DevDisconnected) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.USBD_GetEpDesc) refers to usbd_core.o(.text.USBD_GetEpDesc) for [Anonymous Symbol]
    usbd_core.o(.ARM.exidx.text.USBD_GetNextDesc) refers to usbd_core.o(.text.USBD_GetNextDesc) for [Anonymous Symbol]
    usbd_ctlreq.o(.text.USBD_StdDevReq) refers to usbd_ioreq.o(.text.USBD_CtlSendData) for USBD_CtlSendData
    usbd_ctlreq.o(.text.USBD_StdDevReq) refers to usbd_ioreq.o(.text.USBD_CtlSendStatus) for USBD_CtlSendStatus
    usbd_ctlreq.o(.text.USBD_StdDevReq) refers to usbd_conf.o(.text.USBD_LL_StallEP) for USBD_LL_StallEP
    usbd_ctlreq.o(.text.USBD_StdDevReq) refers to usbd_conf.o(.text.USBD_LL_SetUSBAddress) for USBD_LL_SetUSBAddress
    usbd_ctlreq.o(.text.USBD_StdDevReq) refers to usbd_core.o(.text.USBD_SetClassConfig) for USBD_SetClassConfig
    usbd_ctlreq.o(.text.USBD_StdDevReq) refers to usbd_core.o(.text.USBD_ClrClassConfig) for USBD_ClrClassConfig
    usbd_ctlreq.o(.text.USBD_StdDevReq) refers to usbd_ctlreq.o(.bss.USBD_SetConfig.cfgidx) for [Anonymous Symbol]
    usbd_ctlreq.o(.ARM.exidx.text.USBD_StdDevReq) refers to usbd_ctlreq.o(.text.USBD_StdDevReq) for [Anonymous Symbol]
    usbd_ctlreq.o(.text.USBD_CtlError) refers to usbd_conf.o(.text.USBD_LL_StallEP) for USBD_LL_StallEP
    usbd_ctlreq.o(.ARM.exidx.text.USBD_CtlError) refers to usbd_ctlreq.o(.text.USBD_CtlError) for [Anonymous Symbol]
    usbd_ctlreq.o(.text.USBD_StdItfReq) refers to usbd_conf.o(.text.USBD_LL_StallEP) for USBD_LL_StallEP
    usbd_ctlreq.o(.text.USBD_StdItfReq) refers to usbd_core.o(.text.USBD_CoreFindIF) for USBD_CoreFindIF
    usbd_ctlreq.o(.text.USBD_StdItfReq) refers to usbd_ioreq.o(.text.USBD_CtlSendStatus) for USBD_CtlSendStatus
    usbd_ctlreq.o(.ARM.exidx.text.USBD_StdItfReq) refers to usbd_ctlreq.o(.text.USBD_StdItfReq) for [Anonymous Symbol]
    usbd_ctlreq.o(.text.USBD_StdEPReq) refers to usbd_core.o(.text.USBD_CoreFindEP) for USBD_CoreFindEP
    usbd_ctlreq.o(.text.USBD_StdEPReq) refers to usbd_conf.o(.text.USBD_LL_StallEP) for USBD_LL_StallEP
    usbd_ctlreq.o(.text.USBD_StdEPReq) refers to usbd_ioreq.o(.text.USBD_CtlSendStatus) for USBD_CtlSendStatus
    usbd_ctlreq.o(.text.USBD_StdEPReq) refers to usbd_conf.o(.text.USBD_LL_ClearStallEP) for USBD_LL_ClearStallEP
    usbd_ctlreq.o(.text.USBD_StdEPReq) refers to usbd_conf.o(.text.USBD_LL_IsStallEP) for USBD_LL_IsStallEP
    usbd_ctlreq.o(.text.USBD_StdEPReq) refers to usbd_ioreq.o(.text.USBD_CtlSendData) for USBD_CtlSendData
    usbd_ctlreq.o(.ARM.exidx.text.USBD_StdEPReq) refers to usbd_ctlreq.o(.text.USBD_StdEPReq) for [Anonymous Symbol]
    usbd_ctlreq.o(.ARM.exidx.text.USBD_ParseSetupRequest) refers to usbd_ctlreq.o(.text.USBD_ParseSetupRequest) for [Anonymous Symbol]
    usbd_ctlreq.o(.ARM.exidx.text.USBD_GetString) refers to usbd_ctlreq.o(.text.USBD_GetString) for [Anonymous Symbol]
    usbd_ioreq.o(.text.USBD_CtlSendData) refers to usbd_conf.o(.text.USBD_LL_Transmit) for USBD_LL_Transmit
    usbd_ioreq.o(.ARM.exidx.text.USBD_CtlSendData) refers to usbd_ioreq.o(.text.USBD_CtlSendData) for [Anonymous Symbol]
    usbd_ioreq.o(.text.USBD_CtlContinueSendData) refers to usbd_conf.o(.text.USBD_LL_Transmit) for USBD_LL_Transmit
    usbd_ioreq.o(.ARM.exidx.text.USBD_CtlContinueSendData) refers to usbd_ioreq.o(.text.USBD_CtlContinueSendData) for [Anonymous Symbol]
    usbd_ioreq.o(.text.USBD_CtlPrepareRx) refers to usbd_conf.o(.text.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_ioreq.o(.ARM.exidx.text.USBD_CtlPrepareRx) refers to usbd_ioreq.o(.text.USBD_CtlPrepareRx) for [Anonymous Symbol]
    usbd_ioreq.o(.text.USBD_CtlContinueRx) refers to usbd_conf.o(.text.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_ioreq.o(.ARM.exidx.text.USBD_CtlContinueRx) refers to usbd_ioreq.o(.text.USBD_CtlContinueRx) for [Anonymous Symbol]
    usbd_ioreq.o(.text.USBD_CtlSendStatus) refers to usbd_conf.o(.text.USBD_LL_Transmit) for USBD_LL_Transmit
    usbd_ioreq.o(.ARM.exidx.text.USBD_CtlSendStatus) refers to usbd_ioreq.o(.text.USBD_CtlSendStatus) for [Anonymous Symbol]
    usbd_ioreq.o(.text.USBD_CtlReceiveStatus) refers to usbd_conf.o(.text.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_ioreq.o(.ARM.exidx.text.USBD_CtlReceiveStatus) refers to usbd_ioreq.o(.text.USBD_CtlReceiveStatus) for [Anonymous Symbol]
    usbd_ioreq.o(.text.USBD_GetRxCount) refers to usbd_conf.o(.text.USBD_LL_GetRxDataSize) for USBD_LL_GetRxDataSize
    usbd_ioreq.o(.ARM.exidx.text.USBD_GetRxCount) refers to usbd_ioreq.o(.text.USBD_GetRxCount) for [Anonymous Symbol]
    usbd_customhid.o(.text.USBD_CUSTOM_HID_Init) refers to usbd_conf.o(.text.USBD_static_malloc) for USBD_static_malloc
    usbd_customhid.o(.text.USBD_CUSTOM_HID_Init) refers to usbd_conf.o(.text.USBD_LL_OpenEP) for USBD_LL_OpenEP
    usbd_customhid.o(.text.USBD_CUSTOM_HID_Init) refers to usbd_conf.o(.text.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_Init) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_Init) for [Anonymous Symbol]
    usbd_customhid.o(.text.USBD_CUSTOM_HID_DeInit) refers to usbd_conf.o(.text.USBD_LL_CloseEP) for USBD_LL_CloseEP
    usbd_customhid.o(.text.USBD_CUSTOM_HID_DeInit) refers to usbd_conf.o(.text.USBD_static_free) for USBD_static_free
    usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_DeInit) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_DeInit) for [Anonymous Symbol]
    usbd_customhid.o(.text.USBD_CUSTOM_HID_Setup) refers to usbd_ioreq.o(.text.USBD_CtlSendData) for USBD_CtlSendData
    usbd_customhid.o(.text.USBD_CUSTOM_HID_Setup) refers to usbd_ioreq.o(.text.USBD_CtlPrepareRx) for USBD_CtlPrepareRx
    usbd_customhid.o(.text.USBD_CUSTOM_HID_Setup) refers to usbd_ctlreq.o(.text.USBD_CtlError) for USBD_CtlError
    usbd_customhid.o(.text.USBD_CUSTOM_HID_Setup) refers to usbd_customhid.o(.data.USBD_CUSTOM_HID_Desc) for [Anonymous Symbol]
    usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_Setup) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_Setup) for [Anonymous Symbol]
    usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_EP0_RxReady) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_EP0_RxReady) for [Anonymous Symbol]
    usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_DataIn) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_DataIn) for [Anonymous Symbol]
    usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_DataOut) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_DataOut) for [Anonymous Symbol]
    usbd_customhid.o(.text.USBD_CUSTOM_HID_GetHSCfgDesc) refers to usbd_core.o(.text.USBD_GetEpDesc) for USBD_GetEpDesc
    usbd_customhid.o(.text.USBD_CUSTOM_HID_GetHSCfgDesc) refers to usbd_customhid.o(.data.USBD_CUSTOM_HID_CfgDesc) for [Anonymous Symbol]
    usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_GetHSCfgDesc) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_GetHSCfgDesc) for [Anonymous Symbol]
    usbd_customhid.o(.text.USBD_CUSTOM_HID_GetFSCfgDesc) refers to usbd_core.o(.text.USBD_GetEpDesc) for USBD_GetEpDesc
    usbd_customhid.o(.text.USBD_CUSTOM_HID_GetFSCfgDesc) refers to usbd_customhid.o(.data.USBD_CUSTOM_HID_CfgDesc) for [Anonymous Symbol]
    usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_GetFSCfgDesc) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_GetFSCfgDesc) for [Anonymous Symbol]
    usbd_customhid.o(.text.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc) refers to usbd_core.o(.text.USBD_GetEpDesc) for USBD_GetEpDesc
    usbd_customhid.o(.text.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc) refers to usbd_customhid.o(.data.USBD_CUSTOM_HID_CfgDesc) for [Anonymous Symbol]
    usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc) for [Anonymous Symbol]
    usbd_customhid.o(.text.USBD_CUSTOM_HID_GetDeviceQualifierDesc) refers to usbd_customhid.o(.data.USBD_CUSTOM_HID_DeviceQualifierDesc) for [Anonymous Symbol]
    usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_GetDeviceQualifierDesc) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_GetDeviceQualifierDesc) for [Anonymous Symbol]
    usbd_customhid.o(.text.USBD_CUSTOM_HID_SendReport) refers to usbd_conf.o(.text.USBD_LL_Transmit) for USBD_LL_Transmit
    usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_SendReport) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_SendReport) for [Anonymous Symbol]
    usbd_customhid.o(.text.USBD_CUSTOM_HID_ReceivePacket) refers to usbd_conf.o(.text.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_ReceivePacket) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_ReceivePacket) for [Anonymous Symbol]
    usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_RegisterInterface) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_RegisterInterface) for [Anonymous Symbol]
    usbd_customhid.o(.data.USBD_CUSTOM_HID) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_Init) for USBD_CUSTOM_HID_Init
    usbd_customhid.o(.data.USBD_CUSTOM_HID) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_DeInit) for USBD_CUSTOM_HID_DeInit
    usbd_customhid.o(.data.USBD_CUSTOM_HID) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_Setup) for USBD_CUSTOM_HID_Setup
    usbd_customhid.o(.data.USBD_CUSTOM_HID) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_EP0_RxReady) for USBD_CUSTOM_HID_EP0_RxReady
    usbd_customhid.o(.data.USBD_CUSTOM_HID) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_DataIn) for USBD_CUSTOM_HID_DataIn
    usbd_customhid.o(.data.USBD_CUSTOM_HID) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_DataOut) for USBD_CUSTOM_HID_DataOut
    usbd_customhid.o(.data.USBD_CUSTOM_HID) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_GetHSCfgDesc) for USBD_CUSTOM_HID_GetHSCfgDesc
    usbd_customhid.o(.data.USBD_CUSTOM_HID) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_GetFSCfgDesc) for USBD_CUSTOM_HID_GetFSCfgDesc
    usbd_customhid.o(.data.USBD_CUSTOM_HID) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc) for USBD_CUSTOM_HID_GetOtherSpeedCfgDesc
    usbd_customhid.o(.data.USBD_CUSTOM_HID) refers to usbd_customhid.o(.text.USBD_CUSTOM_HID_GetDeviceQualifierDesc) for USBD_CUSTOM_HID_GetDeviceQualifierDesc
    my_main.o(.text.my_main) refers to system.o(.text.task_handler) for task_handler
    my_main.o(.ARM.exidx.text.my_main) refers to my_main.o(.text.my_main) for [Anonymous Symbol]
    system.o(.text.HAL_IncTick) refers to system.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    system.o(.text.HAL_IncTick) refers to system.o(.bss..L_MergedGlobals.1) for [Anonymous Symbol]
    system.o(.text.HAL_IncTick) refers to stm32g0xx_hal.o(.data..L_MergedGlobals) for uwTickFreq
    system.o(.text.HAL_IncTick) refers to stm32g0xx_hal.o(.bss.uwTick) for uwTick
    system.o(.ARM.exidx.text.HAL_IncTick) refers to system.o(.text.HAL_IncTick) for [Anonymous Symbol]
    system.o(.text.delay_us) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Start) for HAL_TIM_Base_Start
    system.o(.text.delay_us) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Stop) for HAL_TIM_Base_Stop
    system.o(.text.delay_us) refers to tim.o(.bss.htim6) for htim6
    system.o(.ARM.exidx.text.delay_us) refers to system.o(.text.delay_us) for [Anonymous Symbol]
    system.o(.text.delay_ms) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Stop) for HAL_TIM_Base_Stop
    system.o(.text.delay_ms) refers to stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Start) for HAL_TIM_Base_Start
    system.o(.text.delay_ms) refers to tim.o(.bss.htim6) for htim6
    system.o(.ARM.exidx.text.delay_ms) refers to system.o(.text.delay_ms) for [Anonymous Symbol]
    system.o(.ARM.exidx.text.led_handle) refers to system.o(.text.led_handle) for [Anonymous Symbol]
    system.o(.text.task_schedule) refers to system.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    system.o(.text.task_schedule) refers to system.o(.bss..L_MergedGlobals.1) for [Anonymous Symbol]
    system.o(.ARM.exidx.text.task_schedule) refers to system.o(.text.task_schedule) for [Anonymous Symbol]
    system.o(.text.task_handler) refers to scan_key.o(.text.scan_key_pgr) for scan_key_pgr
    system.o(.text.task_handler) refers to system.o(.bss..L_MergedGlobals.1) for [Anonymous Symbol]
    system.o(.ARM.exidx.text.task_handler) refers to system.o(.text.task_handler) for [Anonymous Symbol]
    scan_key.o(.text.key_gpio_read) refers to stm32g0xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    scan_key.o(.ARM.exidx.text.key_gpio_read) refers to scan_key.o(.text.key_gpio_read) for [Anonymous Symbol]
    scan_key.o(.text.key_flash_event) refers to stm32g0xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    scan_key.o(.text.key_flash_event) refers to scan_key.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    scan_key.o(.ARM.exidx.text.key_flash_event) refers to scan_key.o(.text.key_flash_event) for [Anonymous Symbol]
    scan_key.o(.text.key_mode_event) refers to scan_key.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    scan_key.o(.ARM.exidx.text.key_mode_event) refers to scan_key.o(.text.key_mode_event) for [Anonymous Symbol]
    scan_key.o(.ARM.exidx.text.before_scan_key) refers to scan_key.o(.text.before_scan_key) for [Anonymous Symbol]
    scan_key.o(.ARM.exidx.text.after_scan_key) refers to scan_key.o(.text.after_scan_key) for [Anonymous Symbol]
    scan_key.o(.text.key_event_get) refers to stm32g0xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    scan_key.o(.text.key_event_get) refers to scan_key.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    scan_key.o(.ARM.exidx.text.key_event_get) refers to scan_key.o(.text.key_event_get) for [Anonymous Symbol]
    scan_key.o(.text.scan_key_pgr) refers to scan_key.o(.text.key_event_get) for key_event_get
    scan_key.o(.text.scan_key_pgr) refers to scan_key.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    scan_key.o(.ARM.exidx.text.scan_key_pgr) refers to scan_key.o(.text.scan_key_pgr) for [Anonymous Symbol]
    scan_key.o(.data..L_MergedGlobals) refers to scan_key.o(.text.key_flash_event) for key_flash_event
    scan_key.o(.data..L_MergedGlobals) refers to scan_key.o(.text.key_mode_event) for key_mode_event
    scan_key.o(.data..L_MergedGlobals) refers to scan_key.o(.text.before_scan_key) for before_scan_key
    scan_key.o(.data..L_MergedGlobals) refers to scan_key.o(.text.after_scan_key) for after_scan_key
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_idiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32g0b1xx.o(.text) for __user_initial_stackheap
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to tempstk.o(.text) for __temporary_stack_top
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to __main.o(!!!main) for __main
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.text.SystemClock_Config), (114 bytes).
    Removing main.o(.ARM.exidx.text.SystemClock_Config), (8 bytes).
    Removing main.o(.ARM.exidx.text.Error_Handler), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing gpio.o(.text), (0 bytes).
    Removing gpio.o(.ARM.exidx.text.MX_GPIO_Init), (8 bytes).
    Removing spi.o(.text), (0 bytes).
    Removing spi.o(.ARM.exidx.text.MX_SPI3_Init), (8 bytes).
    Removing spi.o(.ARM.exidx.text.HAL_SPI_MspInit), (8 bytes).
    Removing spi.o(.text.HAL_SPI_MspDeInit), (60 bytes).
    Removing spi.o(.ARM.exidx.text.HAL_SPI_MspDeInit), (8 bytes).
    Removing tim.o(.text), (0 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM6_Init), (8 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing tim.o(.text.HAL_TIM_Base_MspDeInit), (32 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing stm32g0xx_it.o(.text), (0 bytes).
    Removing stm32g0xx_it.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing stm32g0xx_it.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing stm32g0xx_it.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing stm32g0xx_it.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing stm32g0xx_it.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing stm32g0xx_hal_msp.o(.text), (0 bytes).
    Removing stm32g0xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing usbd_conf.o(.text), (0 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.HAL_PCD_MspInit), (8 bytes).
    Removing usbd_conf.o(.text.HAL_PCD_MspDeInit), (32 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.HAL_PCD_MspDeInit), (8 bytes).
    Removing usbd_conf.o(.text.HAL_PCD_SetupStageCallback), (22 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.HAL_PCD_SetupStageCallback), (8 bytes).
    Removing usbd_conf.o(.text.HAL_PCD_DataOutStageCallback), (28 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.HAL_PCD_DataOutStageCallback), (8 bytes).
    Removing usbd_conf.o(.text.HAL_PCD_DataInStageCallback), (24 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.HAL_PCD_DataInStageCallback), (8 bytes).
    Removing usbd_conf.o(.text.HAL_PCD_SOFCallback), (14 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.HAL_PCD_SOFCallback), (8 bytes).
    Removing usbd_conf.o(.text.HAL_PCD_ResetCallback), (34 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.HAL_PCD_ResetCallback), (8 bytes).
    Removing usbd_conf.o(.text.HAL_PCD_SuspendCallback), (36 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.HAL_PCD_SuspendCallback), (8 bytes).
    Removing usbd_conf.o(.text.HAL_PCD_ResumeCallback), (40 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.HAL_PCD_ResumeCallback), (8 bytes).
    Removing usbd_conf.o(.text.HAL_PCD_ISOOUTIncompleteCallback), (14 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.HAL_PCD_ISOOUTIncompleteCallback), (8 bytes).
    Removing usbd_conf.o(.text.HAL_PCD_ISOINIncompleteCallback), (14 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.HAL_PCD_ISOINIncompleteCallback), (8 bytes).
    Removing usbd_conf.o(.text.HAL_PCD_ConnectCallback), (14 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.HAL_PCD_ConnectCallback), (8 bytes).
    Removing usbd_conf.o(.text.HAL_PCD_DisconnectCallback), (14 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.HAL_PCD_DisconnectCallback), (8 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.USBD_LL_Init), (8 bytes).
    Removing usbd_conf.o(.text.USBD_LL_DeInit), (36 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.USBD_LL_DeInit), (8 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.USBD_LL_Start), (8 bytes).
    Removing usbd_conf.o(.text.USBD_LL_Stop), (36 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.USBD_LL_Stop), (8 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.USBD_LL_OpenEP), (8 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.USBD_LL_CloseEP), (8 bytes).
    Removing usbd_conf.o(.text.USBD_LL_FlushEP), (36 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.USBD_LL_FlushEP), (8 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.USBD_LL_StallEP), (8 bytes).
    Removing usbd_conf.o(.text.USBD_LL_ClearStallEP), (36 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.USBD_LL_ClearStallEP), (8 bytes).
    Removing usbd_conf.o(.text.USBD_LL_IsStallEP), (42 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.USBD_LL_IsStallEP), (8 bytes).
    Removing usbd_conf.o(.text.USBD_LL_SetUSBAddress), (36 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.USBD_LL_SetUSBAddress), (8 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.USBD_LL_Transmit), (8 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.USBD_LL_PrepareReceive), (8 bytes).
    Removing usbd_conf.o(.text.USBD_LL_GetRxDataSize), (14 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.USBD_LL_GetRxDataSize), (8 bytes).
    Removing usbd_conf.o(.text.HAL_PCDEx_LPM_Callback), (76 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.HAL_PCDEx_LPM_Callback), (8 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.USBD_static_malloc), (8 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.USBD_static_free), (8 bytes).
    Removing usbd_conf.o(.text.USBD_LL_Delay), (8 bytes).
    Removing usbd_conf.o(.ARM.exidx.text.USBD_LL_Delay), (8 bytes).
    Removing usb_device.o(.text), (0 bytes).
    Removing usb_device.o(.ARM.exidx.text.MX_USB_Device_Init), (8 bytes).
    Removing usbd_desc.o(.text), (0 bytes).
    Removing usbd_desc.o(.ARM.exidx.text.USBD_CUSTOM_HID_DeviceDescriptor), (8 bytes).
    Removing usbd_desc.o(.ARM.exidx.text.USBD_CUSTOM_HID_LangIDStrDescriptor), (8 bytes).
    Removing usbd_desc.o(.ARM.exidx.text.USBD_CUSTOM_HID_ManufacturerStrDescriptor), (8 bytes).
    Removing usbd_desc.o(.ARM.exidx.text.USBD_CUSTOM_HID_ProductStrDescriptor), (8 bytes).
    Removing usbd_desc.o(.ARM.exidx.text.USBD_CUSTOM_HID_SerialStrDescriptor), (8 bytes).
    Removing usbd_desc.o(.ARM.exidx.text.USBD_CUSTOM_HID_ConfigStrDescriptor), (8 bytes).
    Removing usbd_desc.o(.ARM.exidx.text.USBD_CUSTOM_HID_InterfaceStrDescriptor), (8 bytes).
    Removing usbd_custom_hid_if.o(.text), (0 bytes).
    Removing usbd_custom_hid_if.o(.ARM.exidx.text.CUSTOM_HID_Init_FS), (8 bytes).
    Removing usbd_custom_hid_if.o(.ARM.exidx.text.CUSTOM_HID_DeInit_FS), (8 bytes).
    Removing usbd_custom_hid_if.o(.ARM.exidx.text.CUSTOM_HID_OutEvent_FS), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text), (0 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_Init), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_MspInit), (2 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_MspInit), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_DeInit), (52 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_DeInit), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_MspDeInit), (2 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_MspDeInit), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_Start), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_Stop), (44 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_Stop), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_IRQHandler), (2624 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_IRQHandler), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_ResetCallback), (2 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_ResetCallback), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_SetAddress), (36 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_SetAddress), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_ResumeCallback), (2 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_ResumeCallback), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_SuspendCallback), (2 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_SuspendCallback), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_SOFCallback), (2 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_SOFCallback), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_DataOutStageCallback), (2 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_DataOutStageCallback), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_DataInStageCallback), (2 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_DataInStageCallback), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_SetupStageCallback), (2 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_SetupStageCallback), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_ISOOUTIncompleteCallback), (2 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_ISOOUTIncompleteCallback), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_ISOINIncompleteCallback), (2 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_ISOINIncompleteCallback), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_ConnectCallback), (2 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_ConnectCallback), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_DisconnectCallback), (2 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_DisconnectCallback), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_DevConnect), (34 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_DevConnect), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_DevDisconnect), (34 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_DevDisconnect), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_EP_Open), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_EP_Close), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_EP_Receive), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_GetRxCount), (18 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_EP_GetRxCount), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_EP_Transmit), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_EP_SetStall), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_ClrStall), (86 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_EP_ClrStall), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Abort), (36 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_EP_Abort), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Flush), (54 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_EP_Flush), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_ActivateRemoteWakeup), (10 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_ActivateRemoteWakeup), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_DeActivateRemoteWakeup), (10 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_DeActivateRemoteWakeup), (8 bytes).
    Removing stm32g0xx_hal_pcd.o(.text.HAL_PCD_GetState), (12 bytes).
    Removing stm32g0xx_hal_pcd.o(.ARM.exidx.text.HAL_PCD_GetState), (8 bytes).
    Removing stm32g0xx_hal_pcd_ex.o(.text), (0 bytes).
    Removing stm32g0xx_hal_pcd_ex.o(.ARM.exidx.text.HAL_PCDEx_PMAConfig), (8 bytes).
    Removing stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_ActivateBCD), (44 bytes).
    Removing stm32g0xx_hal_pcd_ex.o(.ARM.exidx.text.HAL_PCDEx_ActivateBCD), (8 bytes).
    Removing stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_DeActivateBCD), (22 bytes).
    Removing stm32g0xx_hal_pcd_ex.o(.ARM.exidx.text.HAL_PCDEx_DeActivateBCD), (8 bytes).
    Removing stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_BCD_VBUSDetect), (172 bytes).
    Removing stm32g0xx_hal_pcd_ex.o(.ARM.exidx.text.HAL_PCDEx_BCD_VBUSDetect), (8 bytes).
    Removing stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_BCD_Callback), (2 bytes).
    Removing stm32g0xx_hal_pcd_ex.o(.ARM.exidx.text.HAL_PCDEx_BCD_Callback), (8 bytes).
    Removing stm32g0xx_hal_pcd_ex.o(.ARM.exidx.text.HAL_PCDEx_ActivateLPM), (8 bytes).
    Removing stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_DeActivateLPM), (30 bytes).
    Removing stm32g0xx_hal_pcd_ex.o(.ARM.exidx.text.HAL_PCDEx_DeActivateLPM), (8 bytes).
    Removing stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_LPM_Callback), (2 bytes).
    Removing stm32g0xx_hal_pcd_ex.o(.ARM.exidx.text.HAL_PCDEx_LPM_Callback), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text), (0 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_CoreInit), (32 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_CoreInit), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_EnableGlobalInt), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_DisableGlobalInt), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_SetCurrentMode), (44 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_SetCurrentMode), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_DevInit), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_FlushTxFifo), (4 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_FlushTxFifo), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_FlushRxFifo), (4 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_FlushRxFifo), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_ActivateEndpoint), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_DeactivateEndpoint), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_EPStartXfer), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_WritePMA), (288 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_WritePMA), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_EPSetStall), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_EPClearStall), (108 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_EPClearStall), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_EPStopXfer), (112 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_EPStopXfer), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_StopDevice), (16 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_StopDevice), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_SetDevAddress), (16 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_SetDevAddress), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_DevConnect), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_DevDisconnect), (14 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_DevDisconnect), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_ReadInterrupts), (4 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_ReadInterrupts), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_ActivateRemoteWakeup), (12 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_ActivateRemoteWakeup), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_DeActivateRemoteWakeup), (12 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_DeActivateRemoteWakeup), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_ReadPMA), (268 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_ReadPMA), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_HostInit), (44 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_HostInit), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_ResetPort), (34 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_ResetPort), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_GetHostSpeed), (14 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_GetHostSpeed), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_GetCurrentFrame), (12 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_GetCurrentFrame), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_HC_DoubleBuffer), (52 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_HC_DoubleBuffer), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_HC_Init), (120 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_HC_Init), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_HC_StartXfer), (3040 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_HC_StartXfer), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_HC_IN_Halt), (28 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_HC_IN_Halt), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_HC_OUT_Halt), (28 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_HC_OUT_Halt), (8 bytes).
    Removing stm32g0xx_ll_usb.o(.text.USB_StopHost), (32 bytes).
    Removing stm32g0xx_ll_usb.o(.ARM.exidx.text.USB_StopHost), (8 bytes).
    Removing stm32g0xx_hal_rcc.o(.text), (0 bytes).
    Removing stm32g0xx_hal_rcc.o(.text.HAL_RCC_DeInit), (176 bytes).
    Removing stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig), (8 bytes).
    Removing stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig), (8 bytes).
    Removing stm32g0xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq), (136 bytes).
    Removing stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq), (8 bytes).
    Removing stm32g0xx_hal_rcc.o(.text.HAL_RCC_MCOConfig), (100 bytes).
    Removing stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig), (8 bytes).
    Removing stm32g0xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq), (8 bytes).
    Removing stm32g0xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq), (40 bytes).
    Removing stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq), (8 bytes).
    Removing stm32g0xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig), (168 bytes).
    Removing stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig), (8 bytes).
    Removing stm32g0xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig), (52 bytes).
    Removing stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig), (8 bytes).
    Removing stm32g0xx_hal_rcc.o(.text.HAL_RCC_EnableCSS), (20 bytes).
    Removing stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS), (8 bytes).
    Removing stm32g0xx_hal_rcc.o(.text.HAL_RCC_EnableLSECSS), (16 bytes).
    Removing stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableLSECSS), (8 bytes).
    Removing stm32g0xx_hal_rcc.o(.text.HAL_RCC_DisableLSECSS), (16 bytes).
    Removing stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableLSECSS), (8 bytes).
    Removing stm32g0xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler), (44 bytes).
    Removing stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler), (8 bytes).
    Removing stm32g0xx_hal_rcc.o(.text.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback), (8 bytes).
    Removing stm32g0xx_hal_rcc.o(.text.HAL_RCC_LSECSSCallback), (2 bytes).
    Removing stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_LSECSSCallback), (8 bytes).
    Removing stm32g0xx_hal_rcc.o(.text.HAL_RCC_GetResetSource), (28 bytes).
    Removing stm32g0xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetResetSource), (8 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.text), (0 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig), (8 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig), (164 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig), (8 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq), (1412 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq), (8 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnableLSCO), (156 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnableLSCO), (8 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisableLSCO), (92 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisableLSCO), (8 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRSConfig), (76 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRSConfig), (8 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRSSoftwareSynchronizationGenerate), (16 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRSSoftwareSynchronizationGenerate), (8 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRSGetSynchronizationInfo), (44 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRSGetSynchronizationInfo), (8 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRSWaitSynchronization), (324 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRSWaitSynchronization), (8 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_IRQHandler), (148 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRS_IRQHandler), (8 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_SyncOkCallback), (2 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRS_SyncOkCallback), (8 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_SyncWarnCallback), (2 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRS_SyncWarnCallback), (8 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_ExpectedSyncCallback), (2 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRS_ExpectedSyncCallback), (8 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_ErrorCallback), (2 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRS_ErrorCallback), (8 bytes).
    Removing stm32g0xx_ll_rcc.o(.text), (0 bytes).
    Removing stm32g0xx_hal_flash.o(.text), (0 bytes).
    Removing stm32g0xx_hal_flash.o(.text.HAL_FLASH_Program), (444 bytes).
    Removing stm32g0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program), (8 bytes).
    Removing stm32g0xx_hal_flash.o(.text.FLASH_WaitForLastOperation), (192 bytes).
    Removing stm32g0xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation), (8 bytes).
    Removing stm32g0xx_hal_flash.o(.text.HAL_FLASH_Program_IT), (364 bytes).
    Removing stm32g0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT), (8 bytes).
    Removing stm32g0xx_hal_flash.o(.text.HAL_FLASH_IRQHandler), (176 bytes).
    Removing stm32g0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler), (8 bytes).
    Removing stm32g0xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32g0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback), (8 bytes).
    Removing stm32g0xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32g0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback), (8 bytes).
    Removing stm32g0xx_hal_flash.o(.text.HAL_FLASH_Unlock), (40 bytes).
    Removing stm32g0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock), (8 bytes).
    Removing stm32g0xx_hal_flash.o(.text.HAL_FLASH_Lock), (128 bytes).
    Removing stm32g0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock), (8 bytes).
    Removing stm32g0xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32g0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock), (8 bytes).
    Removing stm32g0xx_hal_flash.o(.text.HAL_FLASH_OB_Lock), (132 bytes).
    Removing stm32g0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock), (8 bytes).
    Removing stm32g0xx_hal_flash.o(.text.HAL_FLASH_OB_Launch), (20 bytes).
    Removing stm32g0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch), (8 bytes).
    Removing stm32g0xx_hal_flash.o(.text.HAL_FLASH_GetError), (12 bytes).
    Removing stm32g0xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError), (8 bytes).
    Removing stm32g0xx_hal_flash.o(.bss.pFlash), (28 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.text), (0 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase), (216 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase), (8 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.text.FLASH_PageErase), (44 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_PageErase), (8 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT), (152 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT), (8 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram), (704 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram), (8 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig), (516 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig), (8 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_EnableDebugger), (20 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_EnableDebugger), (8 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_DisableDebugger), (20 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_DisableDebugger), (8 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_FlashEmptyCheck), (16 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_FlashEmptyCheck), (8 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_ForceFlashEmpty), (20 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_ForceFlashEmpty), (8 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.text.HAL_FLASHEx_EnableSecMemProtection), (44 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_EnableSecMemProtection), (8 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.text.FLASH_FlushCaches), (44 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches), (8 bytes).
    Removing stm32g0xx_hal_gpio.o(.text), (0 bytes).
    Removing stm32g0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init), (8 bytes).
    Removing stm32g0xx_hal_gpio.o(.text.HAL_GPIO_DeInit), (252 bytes).
    Removing stm32g0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit), (8 bytes).
    Removing stm32g0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin), (8 bytes).
    Removing stm32g0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin), (8 bytes).
    Removing stm32g0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin), (8 bytes).
    Removing stm32g0xx_hal_gpio.o(.text.HAL_GPIO_LockPin), (40 bytes).
    Removing stm32g0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin), (8 bytes).
    Removing stm32g0xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler), (40 bytes).
    Removing stm32g0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler), (8 bytes).
    Removing stm32g0xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Rising_Callback), (2 bytes).
    Removing stm32g0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Rising_Callback), (8 bytes).
    Removing stm32g0xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Falling_Callback), (2 bytes).
    Removing stm32g0xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Falling_Callback), (8 bytes).
    Removing stm32g0xx_hal_dma.o(.text), (0 bytes).
    Removing stm32g0xx_hal_dma.o(.text.HAL_DMA_Init), (296 bytes).
    Removing stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init), (8 bytes).
    Removing stm32g0xx_hal_dma.o(.text.HAL_DMA_DeInit), (224 bytes).
    Removing stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit), (8 bytes).
    Removing stm32g0xx_hal_dma.o(.text.HAL_DMA_Start), (136 bytes).
    Removing stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start), (8 bytes).
    Removing stm32g0xx_hal_dma.o(.text.HAL_DMA_Start_IT), (194 bytes).
    Removing stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT), (8 bytes).
    Removing stm32g0xx_hal_dma.o(.text.HAL_DMA_Abort), (112 bytes).
    Removing stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort), (8 bytes).
    Removing stm32g0xx_hal_dma.o(.text.HAL_DMA_Abort_IT), (108 bytes).
    Removing stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT), (8 bytes).
    Removing stm32g0xx_hal_dma.o(.text.HAL_DMA_PollForTransfer), (352 bytes).
    Removing stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer), (8 bytes).
    Removing stm32g0xx_hal_dma.o(.text.HAL_DMA_IRQHandler), (232 bytes).
    Removing stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler), (8 bytes).
    Removing stm32g0xx_hal_dma.o(.text.HAL_DMA_RegisterCallback), (48 bytes).
    Removing stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback), (8 bytes).
    Removing stm32g0xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback), (102 bytes).
    Removing stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback), (8 bytes).
    Removing stm32g0xx_hal_dma.o(.text.HAL_DMA_GetState), (6 bytes).
    Removing stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState), (8 bytes).
    Removing stm32g0xx_hal_dma.o(.text.HAL_DMA_GetError), (4 bytes).
    Removing stm32g0xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError), (8 bytes).
    Removing stm32g0xx_hal_dma_ex.o(.text), (0 bytes).
    Removing stm32g0xx_hal_dma_ex.o(.text.HAL_DMAEx_ConfigMuxSync), (80 bytes).
    Removing stm32g0xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ConfigMuxSync), (8 bytes).
    Removing stm32g0xx_hal_dma_ex.o(.text.HAL_DMAEx_ConfigMuxRequestGenerator), (76 bytes).
    Removing stm32g0xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ConfigMuxRequestGenerator), (8 bytes).
    Removing stm32g0xx_hal_dma_ex.o(.text.HAL_DMAEx_EnableMuxRequestGenerator), (32 bytes).
    Removing stm32g0xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_EnableMuxRequestGenerator), (8 bytes).
    Removing stm32g0xx_hal_dma_ex.o(.text.HAL_DMAEx_DisableMuxRequestGenerator), (32 bytes).
    Removing stm32g0xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_DisableMuxRequestGenerator), (8 bytes).
    Removing stm32g0xx_hal_dma_ex.o(.text.HAL_DMAEx_MUX_IRQHandler), (96 bytes).
    Removing stm32g0xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MUX_IRQHandler), (8 bytes).
    Removing stm32g0xx_hal_pwr.o(.text), (0 bytes).
    Removing stm32g0xx_hal_pwr.o(.text.HAL_PWR_DeInit), (24 bytes).
    Removing stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit), (8 bytes).
    Removing stm32g0xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess), (20 bytes).
    Removing stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess), (8 bytes).
    Removing stm32g0xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess), (20 bytes).
    Removing stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess), (8 bytes).
    Removing stm32g0xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin), (8 bytes).
    Removing stm32g0xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin), (24 bytes).
    Removing stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin), (8 bytes).
    Removing stm32g0xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode), (76 bytes).
    Removing stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode), (8 bytes).
    Removing stm32g0xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode), (64 bytes).
    Removing stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode), (8 bytes).
    Removing stm32g0xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode), (36 bytes).
    Removing stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode), (8 bytes).
    Removing stm32g0xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit), (8 bytes).
    Removing stm32g0xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit), (8 bytes).
    Removing stm32g0xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend), (8 bytes).
    Removing stm32g0xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32g0xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text), (0 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBatteryCharging), (28 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBatteryCharging), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBatteryCharging), (20 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBatteryCharging), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnablePORMonitorSampling), (20 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnablePORMonitorSampling), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisablePORMonitorSampling), (20 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisablePORMonitorSampling), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_ConfigPVD), (128 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ConfigPVD), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnablePVD), (16 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnablePVD), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisablePVD), (16 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisablePVD), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableVddUSB), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableVddUSB), (20 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableVddUSB), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableVddIO2), (20 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableVddIO2), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableVddIO2), (20 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableVddIO2), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnablePVMUSB), (20 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnablePVMUSB), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisablePVMUSB), (20 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisablePVMUSB), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_ConfigPVM), (112 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ConfigPVM), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableInternalWakeUpLine), (20 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableInternalWakeUpLine), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableInternalWakeUpLine), (20 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableInternalWakeUpLine), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableGPIOPullUp), (112 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableGPIOPullUp), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableGPIOPullUp), (64 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableGPIOPullUp), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableGPIOPullDown), (124 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableGPIOPullDown), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableGPIOPullDown), (68 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableGPIOPullDown), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnablePullUpPullDownConfig), (20 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnablePullUpPullDownConfig), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisablePullUpPullDownConfig), (20 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisablePullUpPullDownConfig), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableSRAMRetention), (20 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableSRAMRetention), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableSRAMRetention), (20 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableSRAMRetention), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown), (16 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown), (16 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableLowPowerRunMode), (20 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableLowPowerRunMode), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableLowPowerRunMode), (112 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableLowPowerRunMode), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnterSHUTDOWNMode), (36 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnterSHUTDOWNMode), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_PVD_PVM_IRQHandler), (88 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_PVD_PVM_IRQHandler), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_PVD_PVM_Rising_Callback), (2 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_PVD_PVM_Rising_Callback), (8 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_PVD_PVM_Falling_Callback), (2 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_PVD_PVM_Falling_Callback), (8 bytes).
    Removing stm32g0xx_hal_cortex.o(.text), (0 bytes).
    Removing stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority), (8 bytes).
    Removing stm32g0xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ), (24 bytes).
    Removing stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ), (8 bytes).
    Removing stm32g0xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ), (32 bytes).
    Removing stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ), (8 bytes).
    Removing stm32g0xx_hal_cortex.o(.text.HAL_NVIC_SystemReset), (32 bytes).
    Removing stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset), (8 bytes).
    Removing stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config), (8 bytes).
    Removing stm32g0xx_hal_cortex.o(.text.HAL_NVIC_GetPriority), (48 bytes).
    Removing stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority), (8 bytes).
    Removing stm32g0xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ), (24 bytes).
    Removing stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ), (8 bytes).
    Removing stm32g0xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ), (28 bytes).
    Removing stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ), (8 bytes).
    Removing stm32g0xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ), (24 bytes).
    Removing stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ), (8 bytes).
    Removing stm32g0xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig), (28 bytes).
    Removing stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig), (8 bytes).
    Removing stm32g0xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32g0xx_hal_cortex.o(.text.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback), (8 bytes).
    Removing stm32g0xx_hal_cortex.o(.text.HAL_MPU_Enable), (24 bytes).
    Removing stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable), (8 bytes).
    Removing stm32g0xx_hal_cortex.o(.text.HAL_MPU_Disable), (16 bytes).
    Removing stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable), (8 bytes).
    Removing stm32g0xx_hal_cortex.o(.text.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion), (8 bytes).
    Removing stm32g0xx_hal_cortex.o(.text.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion), (8 bytes).
    Removing stm32g0xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32g0xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion), (8 bytes).
    Removing stm32g0xx_hal.o(.text), (0 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_Init), (8 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_InitTick), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_MspInit), (2 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_DeInit), (36 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_DeInit), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_MspDeInit), (2 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_MspDeInit), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_IncTick), (24 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_IncTick), (8 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_GetTick), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_GetTickPrio), (12 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_SetTickFreq), (36 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_GetTickFreq), (12 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_Delay), (36 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_Delay), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_SuspendTick), (16 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_SuspendTick), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_ResumeTick), (16 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_ResumeTick), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_GetHalVersion), (8 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_GetREVID), (12 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_GetREVID), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_GetDEVID), (20 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_GetDEVID), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_GetUIDw0), (12 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_GetUIDw1), (12 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_GetUIDw2), (12 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_SYSCFG_VREFBUF_VoltageScalingConfig), (20 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_VREFBUF_VoltageScalingConfig), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_SYSCFG_VREFBUF_HighImpedanceConfig), (20 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_VREFBUF_HighImpedanceConfig), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_SYSCFG_VREFBUF_TrimmingConfig), (20 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_VREFBUF_TrimmingConfig), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_SYSCFG_EnableVREFBUF), (48 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_EnableVREFBUF), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_SYSCFG_DisableVREFBUF), (16 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_DisableVREFBUF), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_SYSCFG_EnableIOAnalogSwitchBooster), (20 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_EnableIOAnalogSwitchBooster), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_SYSCFG_DisableIOAnalogSwitchBooster), (20 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_DisableIOAnalogSwitchBooster), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_SYSCFG_EnableRemap), (16 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_EnableRemap), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_SYSCFG_DisableRemap), (16 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_DisableRemap), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_SYSCFG_EnableClampingDiode), (16 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_EnableClampingDiode), (8 bytes).
    Removing stm32g0xx_hal.o(.text.HAL_SYSCFG_DisableClampingDiode), (16 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_DisableClampingDiode), (8 bytes).
    Removing stm32g0xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_StrobeDBattpinsConfig), (8 bytes).
    Removing stm32g0xx_hal_exti.o(.text), (0 bytes).
    Removing stm32g0xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine), (176 bytes).
    Removing stm32g0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine), (8 bytes).
    Removing stm32g0xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine), (156 bytes).
    Removing stm32g0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine), (8 bytes).
    Removing stm32g0xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine), (116 bytes).
    Removing stm32g0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine), (8 bytes).
    Removing stm32g0xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback), (34 bytes).
    Removing stm32g0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback), (8 bytes).
    Removing stm32g0xx_hal_exti.o(.text.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32g0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle), (8 bytes).
    Removing stm32g0xx_hal_exti.o(.text.HAL_EXTI_IRQHandler), (68 bytes).
    Removing stm32g0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler), (8 bytes).
    Removing stm32g0xx_hal_exti.o(.text.HAL_EXTI_GetPending), (36 bytes).
    Removing stm32g0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending), (8 bytes).
    Removing stm32g0xx_hal_exti.o(.text.HAL_EXTI_ClearPending), (36 bytes).
    Removing stm32g0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending), (8 bytes).
    Removing stm32g0xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI), (28 bytes).
    Removing stm32g0xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text), (0 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Init), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_MspInit), (2 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_MspInit), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_DeInit), (44 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DeInit), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_MspDeInit), (2 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_MspDeInit), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_Transmit), (540 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Transmit), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_Receive), (552 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Receive), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_TransmitReceive), (752 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TransmitReceive), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_Transmit_IT), (136 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Transmit_IT), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_TxISR_16BIT), (34 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_TxISR_16BIT), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_TxISR_8BIT), (36 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_TxISR_8BIT), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_Receive_IT), (248 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Receive_IT), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_IT), (208 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TransmitReceive_IT), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_RxISR_16BIT), (36 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_RxISR_16BIT), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_RxISR_8BIT), (38 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_RxISR_8BIT), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_2linesRxISR_16BIT), (50 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_2linesRxISR_16BIT), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_2linesTxISR_16BIT), (50 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_2linesTxISR_16BIT), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_2linesRxISR_8BIT), (102 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_2linesRxISR_8BIT), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_2linesTxISR_8BIT), (80 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_2linesTxISR_8BIT), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_Transmit_DMA), (272 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Transmit_DMA), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_DMAHalfTransmitCplt), (10 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_DMAHalfTransmitCplt), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_DMATransmitCplt), (158 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_DMATransmitCplt), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_DMAError), (34 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_DMAError), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_Receive_DMA), (340 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Receive_DMA), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA), (452 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TransmitReceive_DMA), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_DMAHalfReceiveCplt), (10 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_DMAHalfReceiveCplt), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_DMAReceiveCplt), (198 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_DMAReceiveCplt), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_DMAHalfTransmitReceiveCplt), (10 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_DMAHalfTransmitReceiveCplt), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt), (146 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_DMATransmitReceiveCplt), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_Abort), (544 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Abort), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_AbortTx_ISR), (368 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_AbortTx_ISR), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_AbortRx_ISR), (184 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_AbortRx_ISR), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_WaitFifoStateUntilTimeout), (356 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_WaitFifoStateUntilTimeout), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout), (208 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_WaitFlagStateUntilTimeout), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_Abort_IT), (456 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Abort_IT), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_DMATxAbortCallback), (190 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_DMATxAbortCallback), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_DMARxAbortCallback), (140 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_DMARxAbortCallback), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_AbortCpltCallback), (2 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_AbortCpltCallback), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_DMAPause), (34 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DMAPause), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_DMAResume), (34 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DMAResume), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_DMAStop), (74 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DMAStop), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_IRQHandler), (348 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_IRQHandler), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_DMAAbortOnError), (18 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_DMAAbortOnError), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_ErrorCallback), (2 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_ErrorCallback), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_TxCpltCallback), (2 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxCpltCallback), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_RxCpltCallback), (2 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_RxCpltCallback), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_TxRxCpltCallback), (2 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxRxCpltCallback), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_TxHalfCpltCallback), (2 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxHalfCpltCallback), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_RxHalfCpltCallback), (2 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_RxHalfCpltCallback), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_TxRxHalfCpltCallback), (2 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxRxHalfCpltCallback), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_GetState), (6 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_GetState), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.HAL_SPI_GetError), (4 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_GetError), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_CloseRxTx_ISR), (134 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_CloseRxTx_ISR), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_CloseRx_ISR), (154 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_CloseRx_ISR), (8 bytes).
    Removing stm32g0xx_hal_spi.o(.text.SPI_CloseTx_ISR), (138 bytes).
    Removing stm32g0xx_hal_spi.o(.ARM.exidx.text.SPI_CloseTx_ISR), (8 bytes).
    Removing stm32g0xx_hal_spi_ex.o(.text), (0 bytes).
    Removing stm32g0xx_hal_spi_ex.o(.text.HAL_SPIEx_FlushRxFifo), (68 bytes).
    Removing stm32g0xx_hal_spi_ex.o(.ARM.exidx.text.HAL_SPIEx_FlushRxFifo), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text), (0 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_DeInit), (80 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Start), (120 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Stop), (44 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT), (124 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT), (60 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA), (196 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt), (30 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.TIM_DMAError), (80 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA), (72 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Init), (74 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_DeInit), (80 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Start), (240 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.TIM_CCxChannelCmd), (26 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Stop), (172 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT), (264 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT), (180 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA), (608 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.TIM_DMADelayPulseCplt), (104 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA), (196 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Init), (74 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit), (80 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Start), (240 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Stop), (172 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT), (264 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT), (180 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA), (608 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA), (196 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Init), (74 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_DeInit), (80 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Start), (308 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Stop), (156 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT), (332 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT), (184 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA), (516 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.TIM_DMACaptureCplt), (112 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA), (212 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init), (80 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit), (68 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start), (116 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop), (132 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT), (132 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT), (148 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_Init), (156 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit), (68 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_Start), (210 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop), (120 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT), (244 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT), (184 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA), (560 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA), (212 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_IRQHandler), (476 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel), (748 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.TIM_OC2_SetConfig), (128 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel), (416 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.TIM_TI1_SetConfig), (108 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel), (920 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel), (588 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart), (22 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart), (364 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.TIM_DMATriggerCplt), (30 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop), (128 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart), (22 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart), (364 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop), (128 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_GenerateEvent), (34 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear), (320 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.TIM_ETR_SetConfig), (22 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource), (348 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro), (76 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig), (200 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT), (76 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue), (22 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_GetChannelState), (20 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.text.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32g0xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState), (8 bytes).
    Removing stm32g0xx_hal_tim.o(.rodata.cst16), (48 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text), (0 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init), (196 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit), (68 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start), (156 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop), (64 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT), (160 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT), (72 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA), (232 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA), (84 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start), (204 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop), (132 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT), (244 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT), (184 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA), (424 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt), (82 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN), (60 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA), (184 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start), (204 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop), (132 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT), (244 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT), (184 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA), (424 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA), (184 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start), (102 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop), (120 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT), (114 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT), (136 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent), (92 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT), (92 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA), (124 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime), (192 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakInput), (268 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakInput), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig), (40 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_TISelection), (76 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_TISelection), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_GroupChannel5), (50 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GroupChannel5), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_DisarmBreakInput), (72 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_DisarmBreakInput), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_ReArmBreakInput), (150 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ReArmBreakInput), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback), (2 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_Break2Callback), (2 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_Break2Callback), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState), (36 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState), (8 bytes).
    Removing system_stm32g0xx.o(.text), (0 bytes).
    Removing system_stm32g0xx.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_stm32g0xx.o(.text.SystemCoreClockUpdate), (144 bytes).
    Removing system_stm32g0xx.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing system_stm32g0xx.o(.rodata.APBPrescTable), (32 bytes).
    Removing usbd_core.o(.text), (0 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_Init), (8 bytes).
    Removing usbd_core.o(.text.USBD_DeInit), (54 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_DeInit), (8 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_RegisterClass), (8 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_Start), (8 bytes).
    Removing usbd_core.o(.text.USBD_Stop), (30 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_Stop), (8 bytes).
    Removing usbd_core.o(.text.USBD_RunTestMode), (4 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_RunTestMode), (8 bytes).
    Removing usbd_core.o(.text.USBD_SetClassConfig), (22 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_SetClassConfig), (8 bytes).
    Removing usbd_core.o(.text.USBD_ClrClassConfig), (20 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_ClrClassConfig), (8 bytes).
    Removing usbd_core.o(.text.USBD_LL_SetupStage), (98 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_LL_SetupStage), (8 bytes).
    Removing usbd_core.o(.text.USBD_LL_DataOutStage), (116 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_LL_DataOutStage), (8 bytes).
    Removing usbd_core.o(.text.USBD_CoreFindIF), (4 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_CoreFindIF), (8 bytes).
    Removing usbd_core.o(.text.USBD_CoreFindEP), (4 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_CoreFindEP), (8 bytes).
    Removing usbd_core.o(.text.USBD_LL_DataInStage), (180 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_LL_DataInStage), (8 bytes).
    Removing usbd_core.o(.text.USBD_LL_Reset), (116 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_LL_Reset), (8 bytes).
    Removing usbd_core.o(.text.USBD_LL_SetSpeed), (6 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_LL_SetSpeed), (8 bytes).
    Removing usbd_core.o(.text.USBD_LL_Suspend), (18 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_LL_Suspend), (8 bytes).
    Removing usbd_core.o(.text.USBD_LL_Resume), (20 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_LL_Resume), (8 bytes).
    Removing usbd_core.o(.text.USBD_LL_SOF), (32 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_LL_SOF), (8 bytes).
    Removing usbd_core.o(.text.USBD_LL_IsoINIncomplete), (52 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_LL_IsoINIncomplete), (8 bytes).
    Removing usbd_core.o(.text.USBD_LL_IsoOUTIncomplete), (52 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_LL_IsoOUTIncomplete), (8 bytes).
    Removing usbd_core.o(.text.USBD_LL_DevConnected), (4 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_LL_DevConnected), (8 bytes).
    Removing usbd_core.o(.text.USBD_LL_DevDisconnected), (36 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_LL_DevDisconnected), (8 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_GetEpDesc), (8 bytes).
    Removing usbd_core.o(.text.USBD_GetNextDesc), (12 bytes).
    Removing usbd_core.o(.ARM.exidx.text.USBD_GetNextDesc), (8 bytes).
    Removing usbd_ctlreq.o(.text), (0 bytes).
    Removing usbd_ctlreq.o(.text.USBD_StdDevReq), (888 bytes).
    Removing usbd_ctlreq.o(.ARM.exidx.text.USBD_StdDevReq), (8 bytes).
    Removing usbd_ctlreq.o(.ARM.exidx.text.USBD_CtlError), (8 bytes).
    Removing usbd_ctlreq.o(.text.USBD_StdItfReq), (126 bytes).
    Removing usbd_ctlreq.o(.ARM.exidx.text.USBD_StdItfReq), (8 bytes).
    Removing usbd_ctlreq.o(.text.USBD_StdEPReq), (402 bytes).
    Removing usbd_ctlreq.o(.ARM.exidx.text.USBD_StdEPReq), (8 bytes).
    Removing usbd_ctlreq.o(.text.USBD_ParseSetupRequest), (40 bytes).
    Removing usbd_ctlreq.o(.ARM.exidx.text.USBD_ParseSetupRequest), (8 bytes).
    Removing usbd_ctlreq.o(.ARM.exidx.text.USBD_GetString), (8 bytes).
    Removing usbd_ctlreq.o(.bss.USBD_SetConfig.cfgidx), (1 bytes).
    Removing usbd_ioreq.o(.text), (0 bytes).
    Removing usbd_ioreq.o(.ARM.exidx.text.USBD_CtlSendData), (8 bytes).
    Removing usbd_ioreq.o(.text.USBD_CtlContinueSendData), (18 bytes).
    Removing usbd_ioreq.o(.ARM.exidx.text.USBD_CtlContinueSendData), (8 bytes).
    Removing usbd_ioreq.o(.ARM.exidx.text.USBD_CtlPrepareRx), (8 bytes).
    Removing usbd_ioreq.o(.text.USBD_CtlContinueRx), (18 bytes).
    Removing usbd_ioreq.o(.ARM.exidx.text.USBD_CtlContinueRx), (8 bytes).
    Removing usbd_ioreq.o(.text.USBD_CtlSendStatus), (26 bytes).
    Removing usbd_ioreq.o(.ARM.exidx.text.USBD_CtlSendStatus), (8 bytes).
    Removing usbd_ioreq.o(.text.USBD_CtlReceiveStatus), (26 bytes).
    Removing usbd_ioreq.o(.ARM.exidx.text.USBD_CtlReceiveStatus), (8 bytes).
    Removing usbd_ioreq.o(.text.USBD_GetRxCount), (8 bytes).
    Removing usbd_ioreq.o(.ARM.exidx.text.USBD_GetRxCount), (8 bytes).
    Removing usbd_customhid.o(.text), (0 bytes).
    Removing usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_Init), (8 bytes).
    Removing usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_DeInit), (8 bytes).
    Removing usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_Setup), (8 bytes).
    Removing usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_EP0_RxReady), (8 bytes).
    Removing usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_DataIn), (8 bytes).
    Removing usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_DataOut), (8 bytes).
    Removing usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_GetHSCfgDesc), (8 bytes).
    Removing usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_GetFSCfgDesc), (8 bytes).
    Removing usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc), (8 bytes).
    Removing usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_GetDeviceQualifierDesc), (8 bytes).
    Removing usbd_customhid.o(.text.USBD_CUSTOM_HID_SendReport), (72 bytes).
    Removing usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_SendReport), (8 bytes).
    Removing usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_ReceivePacket), (8 bytes).
    Removing usbd_customhid.o(.ARM.exidx.text.USBD_CUSTOM_HID_RegisterInterface), (8 bytes).
    Removing my_main.o(.text), (0 bytes).
    Removing my_main.o(.ARM.exidx.text.my_main), (8 bytes).
    Removing system.o(.text), (0 bytes).
    Removing system.o(.ARM.exidx.text.HAL_IncTick), (8 bytes).
    Removing system.o(.text.delay_us), (96 bytes).
    Removing system.o(.ARM.exidx.text.delay_us), (8 bytes).
    Removing system.o(.text.delay_ms), (116 bytes).
    Removing system.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing system.o(.text.led_handle), (2 bytes).
    Removing system.o(.ARM.exidx.text.led_handle), (8 bytes).
    Removing system.o(.text.task_schedule), (88 bytes).
    Removing system.o(.ARM.exidx.text.task_schedule), (8 bytes).
    Removing system.o(.ARM.exidx.text.task_handler), (8 bytes).
    Removing scan_key.o(.text), (0 bytes).
    Removing scan_key.o(.text.key_gpio_read), (40 bytes).
    Removing scan_key.o(.ARM.exidx.text.key_gpio_read), (8 bytes).
    Removing scan_key.o(.ARM.exidx.text.key_flash_event), (8 bytes).
    Removing scan_key.o(.ARM.exidx.text.key_mode_event), (8 bytes).
    Removing scan_key.o(.ARM.exidx.text.before_scan_key), (8 bytes).
    Removing scan_key.o(.ARM.exidx.text.after_scan_key), (8 bytes).
    Removing scan_key.o(.ARM.exidx.text.key_event_get), (8 bytes).
    Removing scan_key.o(.ARM.exidx.text.scan_key_pgr), (8 bytes).

1133 unused section(s) (total 57097 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatterp.s                 0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  tempstk.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/cfplib/fpinit.c                 0x00000000   Number         0  fpinit.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    gpio.c                                   0x00000000   Number         0  gpio.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    my_main.c                                0x00000000   Number         0  my_main.o ABSOLUTE
    scan_key.c                               0x00000000   Number         0  scan_key.o ABSOLUTE
    spi.c                                    0x00000000   Number         0  spi.o ABSOLUTE
    startup_stm32g0b1xx.s                    0x00000000   Number         0  startup_stm32g0b1xx.o ABSOLUTE
    stm32g0xx_hal.c                          0x00000000   Number         0  stm32g0xx_hal.o ABSOLUTE
    stm32g0xx_hal_cortex.c                   0x00000000   Number         0  stm32g0xx_hal_cortex.o ABSOLUTE
    stm32g0xx_hal_dma.c                      0x00000000   Number         0  stm32g0xx_hal_dma.o ABSOLUTE
    stm32g0xx_hal_dma_ex.c                   0x00000000   Number         0  stm32g0xx_hal_dma_ex.o ABSOLUTE
    stm32g0xx_hal_exti.c                     0x00000000   Number         0  stm32g0xx_hal_exti.o ABSOLUTE
    stm32g0xx_hal_flash.c                    0x00000000   Number         0  stm32g0xx_hal_flash.o ABSOLUTE
    stm32g0xx_hal_flash_ex.c                 0x00000000   Number         0  stm32g0xx_hal_flash_ex.o ABSOLUTE
    stm32g0xx_hal_gpio.c                     0x00000000   Number         0  stm32g0xx_hal_gpio.o ABSOLUTE
    stm32g0xx_hal_msp.c                      0x00000000   Number         0  stm32g0xx_hal_msp.o ABSOLUTE
    stm32g0xx_hal_pcd.c                      0x00000000   Number         0  stm32g0xx_hal_pcd.o ABSOLUTE
    stm32g0xx_hal_pcd_ex.c                   0x00000000   Number         0  stm32g0xx_hal_pcd_ex.o ABSOLUTE
    stm32g0xx_hal_pwr.c                      0x00000000   Number         0  stm32g0xx_hal_pwr.o ABSOLUTE
    stm32g0xx_hal_pwr_ex.c                   0x00000000   Number         0  stm32g0xx_hal_pwr_ex.o ABSOLUTE
    stm32g0xx_hal_rcc.c                      0x00000000   Number         0  stm32g0xx_hal_rcc.o ABSOLUTE
    stm32g0xx_hal_rcc_ex.c                   0x00000000   Number         0  stm32g0xx_hal_rcc_ex.o ABSOLUTE
    stm32g0xx_hal_spi.c                      0x00000000   Number         0  stm32g0xx_hal_spi.o ABSOLUTE
    stm32g0xx_hal_spi_ex.c                   0x00000000   Number         0  stm32g0xx_hal_spi_ex.o ABSOLUTE
    stm32g0xx_hal_tim.c                      0x00000000   Number         0  stm32g0xx_hal_tim.o ABSOLUTE
    stm32g0xx_hal_tim_ex.c                   0x00000000   Number         0  stm32g0xx_hal_tim_ex.o ABSOLUTE
    stm32g0xx_it.c                           0x00000000   Number         0  stm32g0xx_it.o ABSOLUTE
    stm32g0xx_ll_rcc.c                       0x00000000   Number         0  stm32g0xx_ll_rcc.o ABSOLUTE
    stm32g0xx_ll_usb.c                       0x00000000   Number         0  stm32g0xx_ll_usb.o ABSOLUTE
    system.c                                 0x00000000   Number         0  system.o ABSOLUTE
    system_stm32g0xx.c                       0x00000000   Number         0  system_stm32g0xx.o ABSOLUTE
    tim.c                                    0x00000000   Number         0  tim.o ABSOLUTE
    usb_device.c                             0x00000000   Number         0  usb_device.o ABSOLUTE
    usbd_conf.c                              0x00000000   Number         0  usbd_conf.o ABSOLUTE
    usbd_core.c                              0x00000000   Number         0  usbd_core.o ABSOLUTE
    usbd_ctlreq.c                            0x00000000   Number         0  usbd_ctlreq.o ABSOLUTE
    usbd_custom_hid_if.c                     0x00000000   Number         0  usbd_custom_hid_if.o ABSOLUTE
    usbd_customhid.c                         0x00000000   Number         0  usbd_customhid.o ABSOLUTE
    usbd_desc.c                              0x00000000   Number         0  usbd_desc.o ABSOLUTE
    usbd_ioreq.c                             0x00000000   Number         0  usbd_ioreq.o ABSOLUTE
    RESET                                    0x08000000   Section      188  startup_stm32g0b1xx.o(RESET)
    !!!main                                  0x080000bc   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000c4   Section       84  __scatter.o(!!!scatter)
    !!handler_null                           0x08000118   Section        2  __scatter.o(!!handler_null)
    !!handler_zi                             0x0800011c   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x08000138   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000006          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    .ARM.Collect$$libinit$$0000000C          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000010          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    .ARM.Collect$$libinit$$00000013          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$00000027          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    .ARM.Collect$$libinit$$0000002E          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000034          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    .ARM.Collect$$libinit$$00000035          0x0800013a   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000035)
    .ARM.Collect$$libshutdown$$00000000      0x0800013c   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0800013e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0800013e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x0800013e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x0800013e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x0800013e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x0800013e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x0800013e   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000140   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000140   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000140   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000146   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000146   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800014a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800014a   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000152   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000154   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000154   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000158   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000160   Section       56  startup_stm32g0b1xx.o(.text)
    .text                                    0x08000198   Section        0  rt_memclr.o(.text)
    .text                                    0x080001d8   Section        0  heapauxi.o(.text)
    .text                                    0x080001de   Section       62  sys_stackheap_outer.o(.text)
    .text                                    0x0800021c   Section        0  exit.o(.text)
    .text                                    0x0800022c   Section        8  libspace.o(.text)
    .text                                    0x08000234   Section        0  sys_exit.o(.text)
    .text                                    0x08000240   Section        2  use_no_semi.o(.text)
    .text                                    0x08000242   Section        0  indicate_semi.o(.text)
    .text                                    0x08000242   Section        0  __dczerorl2.o(.text)
    CUSTOM_HID_DeInit_FS                     0x08000299   Thumb Code     4  usbd_custom_hid_if.o(.text.CUSTOM_HID_DeInit_FS)
    [Anonymous Symbol]                       0x08000298   Section        0  usbd_custom_hid_if.o(.text.CUSTOM_HID_DeInit_FS)
    CUSTOM_HID_Init_FS                       0x0800029d   Thumb Code     4  usbd_custom_hid_if.o(.text.CUSTOM_HID_Init_FS)
    [Anonymous Symbol]                       0x0800029c   Section        0  usbd_custom_hid_if.o(.text.CUSTOM_HID_Init_FS)
    CUSTOM_HID_OutEvent_FS                   0x080002a1   Thumb Code    20  usbd_custom_hid_if.o(.text.CUSTOM_HID_OutEvent_FS)
    [Anonymous Symbol]                       0x080002a0   Section        0  usbd_custom_hid_if.o(.text.CUSTOM_HID_OutEvent_FS)
    __arm_cp.2_0                             0x080002b4   Number         4  usbd_custom_hid_if.o(.text.CUSTOM_HID_OutEvent_FS)
    [Anonymous Symbol]                       0x080002b8   Section        0  main.o(.text.Error_Handler)
    [Anonymous Symbol]                       0x080002c0   Section        0  stm32g0xx_hal_gpio.o(.text.HAL_GPIO_Init)
    __arm_cp.0_0                             0x0800044c   Number         4  stm32g0xx_hal_gpio.o(.text.HAL_GPIO_Init)
    __arm_cp.0_1                             0x08000450   Number         4  stm32g0xx_hal_gpio.o(.text.HAL_GPIO_Init)
    __arm_cp.0_2                             0x08000454   Number         4  stm32g0xx_hal_gpio.o(.text.HAL_GPIO_Init)
    [Anonymous Symbol]                       0x08000458   Section        0  stm32g0xx_hal_gpio.o(.text.HAL_GPIO_ReadPin)
    [Anonymous Symbol]                       0x08000462   Section        0  stm32g0xx_hal_gpio.o(.text.HAL_GPIO_TogglePin)
    [Anonymous Symbol]                       0x08000472   Section        0  stm32g0xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    [Anonymous Symbol]                       0x08000484   Section        0  stm32g0xx_hal.o(.text.HAL_GetTick)
    [Anonymous Symbol]                       0x0800048c   Section        0  system.o(.text.HAL_IncTick)
    __arm_cp.0_0                             0x080004e4   Number         4  system.o(.text.HAL_IncTick)
    __arm_cp.0_1                             0x080004e8   Number         4  system.o(.text.HAL_IncTick)
    __arm_cp.0_2                             0x080004ec   Number         4  system.o(.text.HAL_IncTick)
    __arm_cp.0_3                             0x080004f0   Number         4  system.o(.text.HAL_IncTick)
    [Anonymous Symbol]                       0x080004f4   Section        0  stm32g0xx_hal.o(.text.HAL_Init)
    __arm_cp.0_0                             0x0800051c   Number         4  stm32g0xx_hal.o(.text.HAL_Init)
    [Anonymous Symbol]                       0x08000520   Section        0  stm32g0xx_hal.o(.text.HAL_InitTick)
    __arm_cp.1_0                             0x08000560   Number         4  stm32g0xx_hal.o(.text.HAL_InitTick)
    __arm_cp.1_1                             0x08000564   Number         4  stm32g0xx_hal.o(.text.HAL_InitTick)
    [Anonymous Symbol]                       0x08000568   Section        0  stm32g0xx_hal_msp.o(.text.HAL_MspInit)
    __arm_cp.0_0                             0x0800059c   Number         4  stm32g0xx_hal_msp.o(.text.HAL_MspInit)
    [Anonymous Symbol]                       0x080005a0   Section        0  stm32g0xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    __arm_cp.0_0                             0x080005d0   Number         4  stm32g0xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    __arm_cp.0_1                             0x080005d4   Number         4  stm32g0xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    [Anonymous Symbol]                       0x080005d8   Section        0  stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_ActivateLPM)
    [Anonymous Symbol]                       0x080005fc   Section        0  stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_PMAConfig)
    [Anonymous Symbol]                       0x0800063c   Section        0  stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Close)
    [Anonymous Symbol]                       0x08000680   Section        0  stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Open)
    [Anonymous Symbol]                       0x080006d0   Section        0  stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Receive)
    [Anonymous Symbol]                       0x080006f8   Section        0  stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_SetStall)
    [Anonymous Symbol]                       0x08000750   Section        0  stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Transmit)
    [Anonymous Symbol]                       0x0800077c   Section        0  stm32g0xx_hal_pcd.o(.text.HAL_PCD_Init)
    [Anonymous Symbol]                       0x080009c0   Section        0  usbd_conf.o(.text.HAL_PCD_MspInit)
    __arm_cp.0_0                             0x08000a30   Number         4  usbd_conf.o(.text.HAL_PCD_MspInit)
    __arm_cp.0_1                             0x08000a34   Number         4  usbd_conf.o(.text.HAL_PCD_MspInit)
    [Anonymous Symbol]                       0x08000a38   Section        0  stm32g0xx_hal_pcd.o(.text.HAL_PCD_Start)
    [Anonymous Symbol]                       0x08000a64   Section        0  stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling)
    __arm_cp.27_0                            0x08000adc   Number         4  stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling)
    __arm_cp.27_1                            0x08000ae0   Number         4  stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling)
    __arm_cp.27_2                            0x08000ae4   Number         4  stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling)
    [Anonymous Symbol]                       0x08000ae8   Section        0  stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableVddUSB)
    __arm_cp.7_0                             0x08000af8   Number         4  stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableVddUSB)
    [Anonymous Symbol]                       0x08000afc   Section        0  stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig)
    __arm_cp.0_0                             0x08000d94   Number         4  stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig)
    __arm_cp.0_1                             0x08000d98   Number         4  stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig)
    __arm_cp.0_2                             0x08000d9c   Number         4  stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig)
    __arm_cp.0_4                             0x08000da0   Number         4  stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig)
    __arm_cp.0_5                             0x08000da4   Number         4  stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig)
    [Anonymous Symbol]                       0x08000da8   Section        0  stm32g0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    __arm_cp.2_0                             0x08000f88   Number         4  stm32g0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    __arm_cp.2_1                             0x08000f8c   Number         4  stm32g0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    __arm_cp.2_2                             0x08000f90   Number         4  stm32g0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    __arm_cp.2_3                             0x08000f94   Number         4  stm32g0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    __arm_cp.2_4                             0x08000f98   Number         4  stm32g0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    __arm_cp.2_5                             0x08000f9c   Number         4  stm32g0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    __arm_cp.2_6                             0x08000fa0   Number         4  stm32g0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    __arm_cp.2_7                             0x08000fa4   Number         4  stm32g0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    [Anonymous Symbol]                       0x08000fa8   Section        0  stm32g0xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    __arm_cp.1_9                             0x0800138c   Number         4  stm32g0xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    __arm_cp.1_1                             0x08001458   Number         4  stm32g0xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    __arm_cp.1_2                             0x0800145c   Number         4  stm32g0xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    __arm_cp.1_3                             0x08001460   Number         4  stm32g0xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    __arm_cp.1_4                             0x08001464   Number         4  stm32g0xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    __arm_cp.1_5                             0x08001468   Number         4  stm32g0xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    __arm_cp.1_6                             0x0800146c   Number         4  stm32g0xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    __arm_cp.1_7                             0x08001470   Number         4  stm32g0xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    [Anonymous Symbol]                       0x08001474   Section        0  stm32g0xx_hal_spi.o(.text.HAL_SPI_Init)
    __arm_cp.0_0                             0x08001568   Number         4  stm32g0xx_hal_spi.o(.text.HAL_SPI_Init)
    [Anonymous Symbol]                       0x0800156c   Section        0  spi.o(.text.HAL_SPI_MspInit)
    [Anonymous Symbol]                       0x080015f0   Section        0  stm32g0xx_hal.o(.text.HAL_SYSCFG_StrobeDBattpinsConfig)
    __arm_cp.34_0                            0x08001600   Number         4  stm32g0xx_hal.o(.text.HAL_SYSCFG_StrobeDBattpinsConfig)
    [Anonymous Symbol]                       0x08001604   Section        0  stm32g0xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    __arm_cp.4_0                             0x0800162c   Number         4  stm32g0xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    __arm_cp.4_1                             0x08001630   Number         4  stm32g0xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    __arm_cp.4_2                             0x08001634   Number         4  stm32g0xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    [Anonymous Symbol]                       0x08001638   Section        0  stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    [Anonymous Symbol]                       0x080016ac   Section        0  stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    [Anonymous Symbol]                       0x080016f8   Section        0  tim.o(.text.HAL_TIM_Base_MspInit)
    __arm_cp.1_1                             0x0800171c   Number         4  tim.o(.text.HAL_TIM_Base_MspInit)
    [Anonymous Symbol]                       0x08001720   Section        0  stm32g0xx_it.o(.text.HardFault_Handler)
    [Anonymous Symbol]                       0x08001724   Section        0  gpio.o(.text.MX_GPIO_Init)
    __arm_cp.0_0                             0x080017f8   Number         4  gpio.o(.text.MX_GPIO_Init)
    __arm_cp.0_1                             0x080017fc   Number         4  gpio.o(.text.MX_GPIO_Init)
    __arm_cp.0_2                             0x08001800   Number         4  gpio.o(.text.MX_GPIO_Init)
    __arm_cp.0_3                             0x08001804   Number         4  gpio.o(.text.MX_GPIO_Init)
    __arm_cp.0_4                             0x08001808   Number         4  gpio.o(.text.MX_GPIO_Init)
    [Anonymous Symbol]                       0x0800180c   Section        0  spi.o(.text.MX_SPI3_Init)
    __arm_cp.0_0                             0x0800184c   Number         4  spi.o(.text.MX_SPI3_Init)
    __arm_cp.0_1                             0x08001850   Number         4  spi.o(.text.MX_SPI3_Init)
    [Anonymous Symbol]                       0x08001854   Section        0  tim.o(.text.MX_TIM6_Init)
    __arm_cp.0_0                             0x08001894   Number         4  tim.o(.text.MX_TIM6_Init)
    __arm_cp.0_1                             0x08001898   Number         4  tim.o(.text.MX_TIM6_Init)
    __arm_cp.0_2                             0x0800189c   Number         4  tim.o(.text.MX_TIM6_Init)
    [Anonymous Symbol]                       0x080018a0   Section        0  usb_device.o(.text.MX_USB_Device_Init)
    __arm_cp.0_0                             0x080018e4   Number         4  usb_device.o(.text.MX_USB_Device_Init)
    __arm_cp.0_1                             0x080018e8   Number         4  usb_device.o(.text.MX_USB_Device_Init)
    __arm_cp.0_2                             0x080018ec   Number         4  usb_device.o(.text.MX_USB_Device_Init)
    __arm_cp.0_3                             0x080018f0   Number         4  usb_device.o(.text.MX_USB_Device_Init)
    [Anonymous Symbol]                       0x080018f4   Section        0  stm32g0xx_it.o(.text.NMI_Handler)
    [Anonymous Symbol]                       0x080018f6   Section        0  stm32g0xx_it.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x080018f8   Section        0  stm32g0xx_it.o(.text.SVC_Handler)
    [Anonymous Symbol]                       0x080018fa   Section        0  stm32g0xx_it.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x08001902   Section        0  system_stm32g0xx.o(.text.SystemInit)
    [Anonymous Symbol]                       0x08001904   Section        0  stm32g0xx_hal_tim.o(.text.TIM_Base_SetConfig)
    __arm_cp.2_0                             0x080019d0   Number         4  stm32g0xx_hal_tim.o(.text.TIM_Base_SetConfig)
    __arm_cp.2_1                             0x080019d4   Number         4  stm32g0xx_hal_tim.o(.text.TIM_Base_SetConfig)
    __arm_cp.2_2                             0x080019d8   Number         4  stm32g0xx_hal_tim.o(.text.TIM_Base_SetConfig)
    __arm_cp.2_3                             0x080019dc   Number         4  stm32g0xx_hal_tim.o(.text.TIM_Base_SetConfig)
    __arm_cp.2_4                             0x080019e0   Number         4  stm32g0xx_hal_tim.o(.text.TIM_Base_SetConfig)
    __arm_cp.2_5                             0x080019e4   Number         4  stm32g0xx_hal_tim.o(.text.TIM_Base_SetConfig)
    __arm_cp.2_6                             0x080019e8   Number         4  stm32g0xx_hal_tim.o(.text.TIM_Base_SetConfig)
    __arm_cp.2_7                             0x080019ec   Number         4  stm32g0xx_hal_tim.o(.text.TIM_Base_SetConfig)
    __arm_cp.2_8                             0x080019f0   Number         4  stm32g0xx_hal_tim.o(.text.TIM_Base_SetConfig)
    __arm_cp.2_9                             0x080019f4   Number         4  stm32g0xx_hal_tim.o(.text.TIM_Base_SetConfig)
    __arm_cp.2_10                            0x080019f8   Number         4  stm32g0xx_hal_tim.o(.text.TIM_Base_SetConfig)
    [Anonymous Symbol]                       0x080019fc   Section        0  usbd_desc.o(.text.USBD_CUSTOM_HID_ConfigStrDescriptor)
    USBD_CUSTOM_HID_DataIn                   0x08001a25   Thumb Code    24  usbd_customhid.o(.text.USBD_CUSTOM_HID_DataIn)
    [Anonymous Symbol]                       0x08001a24   Section        0  usbd_customhid.o(.text.USBD_CUSTOM_HID_DataIn)
    USBD_CUSTOM_HID_DataOut                  0x08001a3d   Thumb Code    44  usbd_customhid.o(.text.USBD_CUSTOM_HID_DataOut)
    [Anonymous Symbol]                       0x08001a3c   Section        0  usbd_customhid.o(.text.USBD_CUSTOM_HID_DataOut)
    USBD_CUSTOM_HID_DeInit                   0x08001a69   Thumb Code    88  usbd_customhid.o(.text.USBD_CUSTOM_HID_DeInit)
    [Anonymous Symbol]                       0x08001a68   Section        0  usbd_customhid.o(.text.USBD_CUSTOM_HID_DeInit)
    [Anonymous Symbol]                       0x08001ac0   Section        0  usbd_desc.o(.text.USBD_CUSTOM_HID_DeviceDescriptor)
    __arm_cp.0_0                             0x08001ac8   Number         4  usbd_desc.o(.text.USBD_CUSTOM_HID_DeviceDescriptor)
    USBD_CUSTOM_HID_EP0_RxReady              0x08001acd   Thumb Code    56  usbd_customhid.o(.text.USBD_CUSTOM_HID_EP0_RxReady)
    [Anonymous Symbol]                       0x08001acc   Section        0  usbd_customhid.o(.text.USBD_CUSTOM_HID_EP0_RxReady)
    USBD_CUSTOM_HID_GetDeviceQualifierDesc   0x08001b05   Thumb Code     8  usbd_customhid.o(.text.USBD_CUSTOM_HID_GetDeviceQualifierDesc)
    [Anonymous Symbol]                       0x08001b04   Section        0  usbd_customhid.o(.text.USBD_CUSTOM_HID_GetDeviceQualifierDesc)
    __arm_cp.9_0                             0x08001b0c   Number         4  usbd_customhid.o(.text.USBD_CUSTOM_HID_GetDeviceQualifierDesc)
    USBD_CUSTOM_HID_GetFSCfgDesc             0x08001b11   Thumb Code    64  usbd_customhid.o(.text.USBD_CUSTOM_HID_GetFSCfgDesc)
    [Anonymous Symbol]                       0x08001b10   Section        0  usbd_customhid.o(.text.USBD_CUSTOM_HID_GetFSCfgDesc)
    USBD_CUSTOM_HID_GetHSCfgDesc             0x08001b51   Thumb Code    64  usbd_customhid.o(.text.USBD_CUSTOM_HID_GetHSCfgDesc)
    [Anonymous Symbol]                       0x08001b50   Section        0  usbd_customhid.o(.text.USBD_CUSTOM_HID_GetHSCfgDesc)
    USBD_CUSTOM_HID_GetOtherSpeedCfgDesc     0x08001b91   Thumb Code    64  usbd_customhid.o(.text.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc)
    [Anonymous Symbol]                       0x08001b90   Section        0  usbd_customhid.o(.text.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc)
    __arm_cp.8_0                             0x08001bd0   Number         4  usbd_customhid.o(.text.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc)
    USBD_CUSTOM_HID_Init                     0x08001bd5   Thumb Code   158  usbd_customhid.o(.text.USBD_CUSTOM_HID_Init)
    [Anonymous Symbol]                       0x08001bd4   Section        0  usbd_customhid.o(.text.USBD_CUSTOM_HID_Init)
    [Anonymous Symbol]                       0x08001c74   Section        0  usbd_desc.o(.text.USBD_CUSTOM_HID_InterfaceStrDescriptor)
    [Anonymous Symbol]                       0x08001ca0   Section        0  usbd_desc.o(.text.USBD_CUSTOM_HID_LangIDStrDescriptor)
    __arm_cp.1_0                             0x08001ca8   Number         4  usbd_desc.o(.text.USBD_CUSTOM_HID_LangIDStrDescriptor)
    [Anonymous Symbol]                       0x08001cac   Section        0  usbd_desc.o(.text.USBD_CUSTOM_HID_ManufacturerStrDescriptor)
    [Anonymous Symbol]                       0x08001cc8   Section        0  usbd_desc.o(.text.USBD_CUSTOM_HID_ProductStrDescriptor)
    __arm_cp.3_1                             0x08001cfc   Number         4  usbd_desc.o(.text.USBD_CUSTOM_HID_ProductStrDescriptor)
    [Anonymous Symbol]                       0x08001d00   Section        0  usbd_customhid.o(.text.USBD_CUSTOM_HID_ReceivePacket)
    [Anonymous Symbol]                       0x08001d26   Section        0  usbd_customhid.o(.text.USBD_CUSTOM_HID_RegisterInterface)
    [Anonymous Symbol]                       0x08001d44   Section        0  usbd_desc.o(.text.USBD_CUSTOM_HID_SerialStrDescriptor)
    __arm_cp.4_0                             0x08001e4c   Number         4  usbd_desc.o(.text.USBD_CUSTOM_HID_SerialStrDescriptor)
    __arm_cp.4_1                             0x08001e50   Number         4  usbd_desc.o(.text.USBD_CUSTOM_HID_SerialStrDescriptor)
    USBD_CUSTOM_HID_Setup                    0x08001e55   Thumb Code   296  usbd_customhid.o(.text.USBD_CUSTOM_HID_Setup)
    [Anonymous Symbol]                       0x08001e54   Section        0  usbd_customhid.o(.text.USBD_CUSTOM_HID_Setup)
    __arm_cp.2_0                             0x08001f7c   Number         4  usbd_customhid.o(.text.USBD_CUSTOM_HID_Setup)
    [Anonymous Symbol]                       0x08001f80   Section        0  usbd_ctlreq.o(.text.USBD_CtlError)
    [Anonymous Symbol]                       0x08001f94   Section        0  usbd_ioreq.o(.text.USBD_CtlPrepareRx)
    [Anonymous Symbol]                       0x08001fb8   Section        0  usbd_ioreq.o(.text.USBD_CtlSendData)
    [Anonymous Symbol]                       0x08001fd6   Section        0  usbd_core.o(.text.USBD_GetEpDesc)
    [Anonymous Symbol]                       0x08002050   Section        0  usbd_ctlreq.o(.text.USBD_GetString)
    [Anonymous Symbol]                       0x080020fc   Section        0  usbd_core.o(.text.USBD_Init)
    [Anonymous Symbol]                       0x0800212c   Section        0  usbd_conf.o(.text.USBD_LL_CloseEP)
    [Anonymous Symbol]                       0x0800214c   Section        0  usbd_conf.o(.text.USBD_LL_Init)
    __arm_cp.13_0                            0x080021bc   Number         4  usbd_conf.o(.text.USBD_LL_Init)
    __arm_cp.13_1                            0x080021c0   Number         4  usbd_conf.o(.text.USBD_LL_Init)
    __arm_cp.13_2                            0x080021c4   Number         4  usbd_conf.o(.text.USBD_LL_Init)
    [Anonymous Symbol]                       0x080021c8   Section        0  usbd_conf.o(.text.USBD_LL_OpenEP)
    [Anonymous Symbol]                       0x080021f0   Section        0  usbd_conf.o(.text.USBD_LL_PrepareReceive)
    [Anonymous Symbol]                       0x08002210   Section        0  usbd_conf.o(.text.USBD_LL_StallEP)
    [Anonymous Symbol]                       0x08002230   Section        0  usbd_conf.o(.text.USBD_LL_Start)
    [Anonymous Symbol]                       0x08002250   Section        0  usbd_conf.o(.text.USBD_LL_Transmit)
    __arm_cp.24_0                            0x08002270   Number         4  usbd_conf.o(.text.USBD_LL_Transmit)
    [Anonymous Symbol]                       0x08002274   Section        0  usbd_core.o(.text.USBD_RegisterClass)
    [Anonymous Symbol]                       0x080022b6   Section        0  usbd_core.o(.text.USBD_Start)
    [Anonymous Symbol]                       0x080022be   Section        0  usbd_conf.o(.text.USBD_static_free)
    [Anonymous Symbol]                       0x080022c0   Section        0  usbd_conf.o(.text.USBD_static_malloc)
    __arm_cp.28_0                            0x080022c4   Number         4  usbd_conf.o(.text.USBD_static_malloc)
    [Anonymous Symbol]                       0x080022c8   Section        0  stm32g0xx_ll_usb.o(.text.USB_ActivateEndpoint)
    __arm_cp.7_0                             0x080024cc   Number         4  stm32g0xx_ll_usb.o(.text.USB_ActivateEndpoint)
    __arm_cp.7_1                             0x080024d0   Number         4  stm32g0xx_ll_usb.o(.text.USB_ActivateEndpoint)
    __arm_cp.7_3                             0x080024d4   Number         4  stm32g0xx_ll_usb.o(.text.USB_ActivateEndpoint)
    __arm_cp.7_4                             0x080024d8   Number         4  stm32g0xx_ll_usb.o(.text.USB_ActivateEndpoint)
    __arm_cp.7_5                             0x080024dc   Number         4  stm32g0xx_ll_usb.o(.text.USB_ActivateEndpoint)
    __arm_cp.7_6                             0x080024e0   Number         4  stm32g0xx_ll_usb.o(.text.USB_ActivateEndpoint)
    __arm_cp.7_9                             0x080024e4   Number         4  stm32g0xx_ll_usb.o(.text.USB_ActivateEndpoint)
    __arm_cp.7_10                            0x080024e8   Number         4  stm32g0xx_ll_usb.o(.text.USB_ActivateEndpoint)
    [Anonymous Symbol]                       0x080024ec   Section        0  stm32g0xx_ll_usb.o(.text.USB_DeactivateEndpoint)
    __arm_cp.8_0                             0x08002590   Number         4  stm32g0xx_ll_usb.o(.text.USB_DeactivateEndpoint)
    __arm_cp.8_1                             0x08002594   Number         4  stm32g0xx_ll_usb.o(.text.USB_DeactivateEndpoint)
    __arm_cp.8_3                             0x08002598   Number         4  stm32g0xx_ll_usb.o(.text.USB_DeactivateEndpoint)
    [Anonymous Symbol]                       0x0800259c   Section        0  stm32g0xx_ll_usb.o(.text.USB_DevConnect)
    [Anonymous Symbol]                       0x080025aa   Section        0  stm32g0xx_ll_usb.o(.text.USB_DevInit)
    [Anonymous Symbol]                       0x080025c4   Section        0  stm32g0xx_ll_usb.o(.text.USB_DisableGlobalInt)
    __arm_cp.2_0                             0x080025d0   Number         4  stm32g0xx_ll_usb.o(.text.USB_DisableGlobalInt)
    [Anonymous Symbol]                       0x080025d4   Section        0  stm32g0xx_ll_usb.o(.text.USB_EPSetStall)
    __arm_cp.11_0                            0x08002600   Number         4  stm32g0xx_ll_usb.o(.text.USB_EPSetStall)
    __arm_cp.11_1                            0x08002604   Number         4  stm32g0xx_ll_usb.o(.text.USB_EPSetStall)
    __arm_cp.11_2                            0x08002608   Number         4  stm32g0xx_ll_usb.o(.text.USB_EPSetStall)
    __arm_cp.11_3                            0x0800260c   Number         4  stm32g0xx_ll_usb.o(.text.USB_EPSetStall)
    [Anonymous Symbol]                       0x08002610   Section        0  stm32g0xx_ll_usb.o(.text.USB_EPStartXfer)
    __arm_cp.9_9                             0x08002a2c   Number         4  stm32g0xx_ll_usb.o(.text.USB_EPStartXfer)
    __arm_cp.9_10                            0x08002a30   Number         4  stm32g0xx_ll_usb.o(.text.USB_EPStartXfer)
    __arm_cp.9_12                            0x08002a38   Number         4  stm32g0xx_ll_usb.o(.text.USB_EPStartXfer)
    __arm_cp.9_17                            0x08002ee8   Number         4  stm32g0xx_ll_usb.o(.text.USB_EPStartXfer)
    __arm_cp.9_18                            0x08002eec   Number         4  stm32g0xx_ll_usb.o(.text.USB_EPStartXfer)
    __arm_cp.9_19                            0x08002ef0   Number         4  stm32g0xx_ll_usb.o(.text.USB_EPStartXfer)
    __arm_cp.9_0                             0x08003230   Number         4  stm32g0xx_ll_usb.o(.text.USB_EPStartXfer)
    __arm_cp.9_3                             0x08003234   Number         4  stm32g0xx_ll_usb.o(.text.USB_EPStartXfer)
    __arm_cp.9_4                             0x08003238   Number         4  stm32g0xx_ll_usb.o(.text.USB_EPStartXfer)
    [Anonymous Symbol]                       0x0800323c   Section        0  stm32g0xx_ll_usb.o(.text.USB_EnableGlobalInt)
    __arm_cp.1_0                             0x08003248   Number         4  stm32g0xx_ll_usb.o(.text.USB_EnableGlobalInt)
    [Anonymous Symbol]                       0x0800324c   Section        0  scan_key.o(.text.after_scan_key)
    [Anonymous Symbol]                       0x0800324e   Section        0  scan_key.o(.text.before_scan_key)
    [Anonymous Symbol]                       0x08003254   Section        0  scan_key.o(.text.key_event_get)
    __arm_cp.5_1                             0x08003390   Number         4  scan_key.o(.text.key_event_get)
    [Anonymous Symbol]                       0x08003394   Section        0  scan_key.o(.text.key_flash_event)
    __arm_cp.1_1                             0x080033a8   Number         4  scan_key.o(.text.key_flash_event)
    [Anonymous Symbol]                       0x080033ac   Section        0  scan_key.o(.text.key_mode_event)
    [Anonymous Symbol]                       0x080033b4   Section        0  main.o(.text.main)
    [Anonymous Symbol]                       0x0800343a   Section        0  my_main.o(.text.my_main)
    [Anonymous Symbol]                       0x08003440   Section        0  scan_key.o(.text.scan_key_pgr)
    __arm_cp.6_0                             0x08003454   Number         4  scan_key.o(.text.scan_key_pgr)
    [Anonymous Symbol]                       0x08003458   Section        0  system.o(.text.task_handler)
    __arm_cp.5_0                             0x08003480   Number         4  system.o(.text.task_handler)
    .text_divfast                            0x08003484   Section      502  aeabi_sdivfast.o(.text_divfast)
    [Anonymous Symbol]                       0x20000000   Section        0  stm32g0xx_hal.o(.data..L_MergedGlobals)
    TaskComps.1                              0x20000008   Data           2  system.o(.data..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000008   Section        0  system.o(.data..L_MergedGlobals)
    TaskComps.5                              0x2000000c   Data           2  system.o(.data..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000010   Section        0  scan_key.o(.data..L_MergedGlobals)
    CUSTOM_HID_ReportDesc_FS                 0x20000064   Data          25  usbd_custom_hid_if.o(.data.CUSTOM_HID_ReportDesc_FS)
    [Anonymous Symbol]                       0x20000064   Section        0  usbd_custom_hid_if.o(.data.CUSTOM_HID_ReportDesc_FS)
    USBD_CUSTOM_HID_CfgDesc                  0x200000bc   Data          41  usbd_customhid.o(.data.USBD_CUSTOM_HID_CfgDesc)
    [Anonymous Symbol]                       0x200000bc   Section        0  usbd_customhid.o(.data.USBD_CUSTOM_HID_CfgDesc)
    USBD_CUSTOM_HID_Desc                     0x200000e8   Data           9  usbd_customhid.o(.data.USBD_CUSTOM_HID_Desc)
    [Anonymous Symbol]                       0x200000e8   Section        0  usbd_customhid.o(.data.USBD_CUSTOM_HID_Desc)
    USBD_CUSTOM_HID_DeviceQualifierDesc      0x20000108   Data          10  usbd_customhid.o(.data.USBD_CUSTOM_HID_DeviceQualifierDesc)
    [Anonymous Symbol]                       0x20000108   Section        0  usbd_customhid.o(.data.USBD_CUSTOM_HID_DeviceQualifierDesc)
    .bss                                     0x20000148   Section       96  libspace.o(.bss)
    TaskComps.0                              0x200001a8   Data           1  system.o(.bss..L_MergedGlobals.1)
    [Anonymous Symbol]                       0x200001a8   Section        0  system.o(.bss..L_MergedGlobals.1)
    TaskComps.4                              0x200001ac   Data           1  system.o(.bss..L_MergedGlobals.1)
    TaskComps.8                              0x200001b0   Data           1  system.o(.bss..L_MergedGlobals.1)
    TaskComps.9                              0x200001b4   Data           2  system.o(.bss..L_MergedGlobals.1)
    USBD_static_malloc.mem                   0x200003b8   Data          88  usbd_conf.o(.bss.USBD_static_malloc.mem)
    [Anonymous Symbol]                       0x200003b8   Section        0  usbd_conf.o(.bss.USBD_static_malloc.mem)
    Heap_Mem                                 0x20000a80   Data         512  startup_stm32g0b1xx.o(HEAP)
    HEAP                                     0x20000a80   Section      512  startup_stm32g0b1xx.o(HEAP)
    Stack_Mem                                0x20000c80   Data        1024  startup_stm32g0b1xx.o(STACK)
    STACK                                    0x20000c80   Section     1024  startup_stm32g0b1xx.o(STACK)
    __initial_sp                             0x20001080   Data           0  startup_stm32g0b1xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __arm_relocate_pie_                       - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000bc   Number         0  startup_stm32g0b1xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32g0b1xx.o(RESET)
    __Vectors_End                            0x080000bc   Data           0  startup_stm32g0b1xx.o(RESET)
    __main                                   0x080000bd   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000c5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000c5   Thumb Code    74  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000c5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_loop                       0x080000cf   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000119   Thumb Code     2  __scatter.o(!!handler_null)
    __scatterload_zeroinit                   0x0800011d   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x08000139   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_argv_1                     0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_atexit_1                   0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_clock_1                    0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_cpp_1                      0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    __rt_lib_init_exceptions_1               0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_fp_1                       0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_getenv_1                   0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_heap_1                     0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_ctype_1                 0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_monetary_1              0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_numeric_1               0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_lc_time_1                  0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_preinit_1                  0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    __rt_lib_init_rand_1                     0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    __rt_lib_init_relocate_pie_1             0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_return                     0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000035)
    __rt_lib_init_signal_1                   0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_stdio_1                    0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    __rt_lib_init_user_alloc_1               0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_shutdown                        0x0800013d   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0800013f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0800013f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x0800013f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x0800013f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x0800013f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x0800013f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x0800013f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000141   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000141   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000141   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000147   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000147   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800014b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800014b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000153   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000155   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000155   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000159   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000161   Thumb Code     8  startup_stm32g0b1xx.o(.text)
    ADC1_COMP_IRQHandler                     0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    CEC_IRQHandler                           0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    DMA1_Ch4_7_DMA2_Ch1_5_DMAMUX1_OVR_IRQHandler 0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    DMA1_Channel1_IRQHandler                 0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    DMA1_Channel2_3_IRQHandler               0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    EXTI0_1_IRQHandler                       0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    EXTI2_3_IRQHandler                       0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    EXTI4_15_IRQHandler                      0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    FLASH_IRQHandler                         0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    I2C1_IRQHandler                          0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    I2C2_3_IRQHandler                        0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    PVD_VDDIO2_IRQHandler                    0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    RCC_CRS_IRQHandler                       0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    RTC_TAMP_IRQHandler                      0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    SPI1_IRQHandler                          0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    SPI2_3_IRQHandler                        0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    TIM14_IRQHandler                         0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    TIM15_IRQHandler                         0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    TIM16_FDCAN_IT0_IRQHandler               0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    TIM17_FDCAN_IT1_IRQHandler               0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    TIM1_BRK_UP_TRG_COM_IRQHandler           0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    TIM1_CC_IRQHandler                       0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    TIM2_IRQHandler                          0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    TIM3_TIM4_IRQHandler                     0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    TIM6_DAC_LPTIM1_IRQHandler               0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    TIM7_LPTIM2_IRQHandler                   0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    USART1_IRQHandler                        0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    USART2_LPUART2_IRQHandler                0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    USART3_4_5_6_LPUART1_IRQHandler          0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    USB_UCPD1_2_IRQHandler                   0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    WWDG_IRQHandler                          0x08000173   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    __user_initial_stackheap                 0x08000175   Thumb Code     0  startup_stm32g0b1xx.o(.text)
    _memset_w                                0x08000199   Thumb Code    26  rt_memclr.o(.text)
    _memset                                  0x080001b3   Thumb Code    30  rt_memclr.o(.text)
    __aeabi_memclr                           0x080001d1   Thumb Code     4  rt_memclr.o(.text)
    __rt_memclr                              0x080001d1   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x080001d5   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr8                          0x080001d5   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr_w                            0x080001d5   Thumb Code     4  rt_memclr.o(.text)
    __use_two_region_memory                  0x080001d9   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080001db   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080001dd   Thumb Code     2  heapauxi.o(.text)
    __user_setup_stackheap                   0x080001df   Thumb Code    62  sys_stackheap_outer.o(.text)
    exit                                     0x0800021d   Thumb Code    16  exit.o(.text)
    __user_libspace                          0x0800022d   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x0800022d   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x0800022d   Thumb Code     0  libspace.o(.text)
    _sys_exit                                0x08000235   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08000241   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000241   Thumb Code     2  use_no_semi.o(.text)
    __decompress                             0x08000243   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x08000243   Thumb Code    86  __dczerorl2.o(.text)
    __semihosting_library_function           0x08000243   Thumb Code     0  indicate_semi.o(.text)
    Error_Handler                            0x080002b9   Thumb Code     8  main.o(.text.Error_Handler)
    HAL_GPIO_Init                            0x080002c1   Thumb Code   396  stm32g0xx_hal_gpio.o(.text.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08000459   Thumb Code    10  stm32g0xx_hal_gpio.o(.text.HAL_GPIO_ReadPin)
    HAL_GPIO_TogglePin                       0x08000463   Thumb Code    16  stm32g0xx_hal_gpio.o(.text.HAL_GPIO_TogglePin)
    HAL_GPIO_WritePin                        0x08000473   Thumb Code    16  stm32g0xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08000485   Thumb Code     8  stm32g0xx_hal.o(.text.HAL_GetTick)
    HAL_IncTick                              0x0800048d   Thumb Code    88  system.o(.text.HAL_IncTick)
    HAL_Init                                 0x080004f5   Thumb Code    40  stm32g0xx_hal.o(.text.HAL_Init)
    HAL_InitTick                             0x08000521   Thumb Code    64  stm32g0xx_hal.o(.text.HAL_InitTick)
    HAL_MspInit                              0x08000569   Thumb Code    52  stm32g0xx_hal_msp.o(.text.HAL_MspInit)
    HAL_NVIC_SetPriority                     0x080005a1   Thumb Code    48  stm32g0xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    HAL_PCDEx_ActivateLPM                    0x080005d9   Thumb Code    36  stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_ActivateLPM)
    HAL_PCDEx_PMAConfig                      0x080005fd   Thumb Code    64  stm32g0xx_hal_pcd_ex.o(.text.HAL_PCDEx_PMAConfig)
    HAL_PCD_EP_Close                         0x0800063d   Thumb Code    68  stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Close)
    HAL_PCD_EP_Open                          0x08000681   Thumb Code    80  stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Open)
    HAL_PCD_EP_Receive                       0x080006d1   Thumb Code    40  stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Receive)
    HAL_PCD_EP_SetStall                      0x080006f9   Thumb Code    88  stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_SetStall)
    HAL_PCD_EP_Transmit                      0x08000751   Thumb Code    44  stm32g0xx_hal_pcd.o(.text.HAL_PCD_EP_Transmit)
    HAL_PCD_Init                             0x0800077d   Thumb Code   580  stm32g0xx_hal_pcd.o(.text.HAL_PCD_Init)
    HAL_PCD_MspInit                          0x080009c1   Thumb Code   112  usbd_conf.o(.text.HAL_PCD_MspInit)
    HAL_PCD_Start                            0x08000a39   Thumb Code    44  stm32g0xx_hal_pcd.o(.text.HAL_PCD_Start)
    HAL_PWREx_ControlVoltageScaling          0x08000a65   Thumb Code   120  stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling)
    HAL_PWREx_EnableVddUSB                   0x08000ae9   Thumb Code    16  stm32g0xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableVddUSB)
    HAL_RCCEx_PeriphCLKConfig                0x08000afd   Thumb Code   664  stm32g0xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x08000da9   Thumb Code   480  stm32g0xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    HAL_RCC_OscConfig                        0x08000fa9   Thumb Code  1200  stm32g0xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    HAL_SPI_Init                             0x08001475   Thumb Code   244  stm32g0xx_hal_spi.o(.text.HAL_SPI_Init)
    HAL_SPI_MspInit                          0x0800156d   Thumb Code   132  spi.o(.text.HAL_SPI_MspInit)
    HAL_SYSCFG_StrobeDBattpinsConfig         0x080015f1   Thumb Code    16  stm32g0xx_hal.o(.text.HAL_SYSCFG_StrobeDBattpinsConfig)
    HAL_SYSTICK_Config                       0x08001605   Thumb Code    40  stm32g0xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    HAL_TIMEx_MasterConfigSynchronization    0x08001639   Thumb Code   116  stm32g0xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x080016ad   Thumb Code    74  stm32g0xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x080016f9   Thumb Code    36  tim.o(.text.HAL_TIM_Base_MspInit)
    HardFault_Handler                        0x08001721   Thumb Code     2  stm32g0xx_it.o(.text.HardFault_Handler)
    MX_GPIO_Init                             0x08001725   Thumb Code   212  gpio.o(.text.MX_GPIO_Init)
    MX_SPI3_Init                             0x0800180d   Thumb Code    64  spi.o(.text.MX_SPI3_Init)
    MX_TIM6_Init                             0x08001855   Thumb Code    64  tim.o(.text.MX_TIM6_Init)
    MX_USB_Device_Init                       0x080018a1   Thumb Code    68  usb_device.o(.text.MX_USB_Device_Init)
    NMI_Handler                              0x080018f5   Thumb Code     2  stm32g0xx_it.o(.text.NMI_Handler)
    PendSV_Handler                           0x080018f7   Thumb Code     2  stm32g0xx_it.o(.text.PendSV_Handler)
    SVC_Handler                              0x080018f9   Thumb Code     2  stm32g0xx_it.o(.text.SVC_Handler)
    SysTick_Handler                          0x080018fb   Thumb Code     8  stm32g0xx_it.o(.text.SysTick_Handler)
    SystemInit                               0x08001903   Thumb Code     2  system_stm32g0xx.o(.text.SystemInit)
    TIM_Base_SetConfig                       0x08001905   Thumb Code   204  stm32g0xx_hal_tim.o(.text.TIM_Base_SetConfig)
    USBD_CUSTOM_HID_ConfigStrDescriptor      0x080019fd   Thumb Code    20  usbd_desc.o(.text.USBD_CUSTOM_HID_ConfigStrDescriptor)
    USBD_CUSTOM_HID_DeviceDescriptor         0x08001ac1   Thumb Code     8  usbd_desc.o(.text.USBD_CUSTOM_HID_DeviceDescriptor)
    USBD_CUSTOM_HID_InterfaceStrDescriptor   0x08001c75   Thumb Code    20  usbd_desc.o(.text.USBD_CUSTOM_HID_InterfaceStrDescriptor)
    USBD_CUSTOM_HID_LangIDStrDescriptor      0x08001ca1   Thumb Code     8  usbd_desc.o(.text.USBD_CUSTOM_HID_LangIDStrDescriptor)
    USBD_CUSTOM_HID_ManufacturerStrDescriptor 0x08001cad   Thumb Code    20  usbd_desc.o(.text.USBD_CUSTOM_HID_ManufacturerStrDescriptor)
    USBD_CUSTOM_HID_ProductStrDescriptor     0x08001cc9   Thumb Code    20  usbd_desc.o(.text.USBD_CUSTOM_HID_ProductStrDescriptor)
    USBD_CUSTOM_HID_ReceivePacket            0x08001d01   Thumb Code    38  usbd_customhid.o(.text.USBD_CUSTOM_HID_ReceivePacket)
    USBD_CUSTOM_HID_RegisterInterface        0x08001d27   Thumb Code    28  usbd_customhid.o(.text.USBD_CUSTOM_HID_RegisterInterface)
    USBD_CUSTOM_HID_SerialStrDescriptor      0x08001d45   Thumb Code   264  usbd_desc.o(.text.USBD_CUSTOM_HID_SerialStrDescriptor)
    USBD_CtlError                            0x08001f81   Thumb Code    20  usbd_ctlreq.o(.text.USBD_CtlError)
    USBD_CtlPrepareRx                        0x08001f95   Thumb Code    36  usbd_ioreq.o(.text.USBD_CtlPrepareRx)
    USBD_CtlSendData                         0x08001fb9   Thumb Code    30  usbd_ioreq.o(.text.USBD_CtlSendData)
    USBD_GetEpDesc                           0x08001fd7   Thumb Code   122  usbd_core.o(.text.USBD_GetEpDesc)
    USBD_GetString                           0x08002051   Thumb Code   172  usbd_ctlreq.o(.text.USBD_GetString)
    USBD_Init                                0x080020fd   Thumb Code    48  usbd_core.o(.text.USBD_Init)
    USBD_LL_CloseEP                          0x0800212d   Thumb Code    32  usbd_conf.o(.text.USBD_LL_CloseEP)
    USBD_LL_Init                             0x0800214d   Thumb Code   112  usbd_conf.o(.text.USBD_LL_Init)
    USBD_LL_OpenEP                           0x080021c9   Thumb Code    40  usbd_conf.o(.text.USBD_LL_OpenEP)
    USBD_LL_PrepareReceive                   0x080021f1   Thumb Code    32  usbd_conf.o(.text.USBD_LL_PrepareReceive)
    USBD_LL_StallEP                          0x08002211   Thumb Code    32  usbd_conf.o(.text.USBD_LL_StallEP)
    USBD_LL_Start                            0x08002231   Thumb Code    32  usbd_conf.o(.text.USBD_LL_Start)
    USBD_LL_Transmit                         0x08002251   Thumb Code    32  usbd_conf.o(.text.USBD_LL_Transmit)
    USBD_RegisterClass                       0x08002275   Thumb Code    66  usbd_core.o(.text.USBD_RegisterClass)
    USBD_Start                               0x080022b7   Thumb Code     8  usbd_core.o(.text.USBD_Start)
    USBD_static_free                         0x080022bf   Thumb Code     2  usbd_conf.o(.text.USBD_static_free)
    USBD_static_malloc                       0x080022c1   Thumb Code     4  usbd_conf.o(.text.USBD_static_malloc)
    USB_ActivateEndpoint                     0x080022c9   Thumb Code   516  stm32g0xx_ll_usb.o(.text.USB_ActivateEndpoint)
    USB_DeactivateEndpoint                   0x080024ed   Thumb Code   164  stm32g0xx_ll_usb.o(.text.USB_DeactivateEndpoint)
    USB_DevConnect                           0x0800259d   Thumb Code    14  stm32g0xx_ll_usb.o(.text.USB_DevConnect)
    USB_DevInit                              0x080025ab   Thumb Code    26  stm32g0xx_ll_usb.o(.text.USB_DevInit)
    USB_DisableGlobalInt                     0x080025c5   Thumb Code    12  stm32g0xx_ll_usb.o(.text.USB_DisableGlobalInt)
    USB_EPSetStall                           0x080025d5   Thumb Code    44  stm32g0xx_ll_usb.o(.text.USB_EPSetStall)
    USB_EPStartXfer                          0x08002611   Thumb Code  3104  stm32g0xx_ll_usb.o(.text.USB_EPStartXfer)
    USB_EnableGlobalInt                      0x0800323d   Thumb Code    12  stm32g0xx_ll_usb.o(.text.USB_EnableGlobalInt)
    after_scan_key                           0x0800324d   Thumb Code     2  scan_key.o(.text.after_scan_key)
    before_scan_key                          0x0800324f   Thumb Code     4  scan_key.o(.text.before_scan_key)
    key_event_get                            0x08003255   Thumb Code   316  scan_key.o(.text.key_event_get)
    key_flash_event                          0x08003395   Thumb Code    20  scan_key.o(.text.key_flash_event)
    key_mode_event                           0x080033ad   Thumb Code     8  scan_key.o(.text.key_mode_event)
    main                                     0x080033b5   Thumb Code   134  main.o(.text.main)
    my_main                                  0x0800343b   Thumb Code     6  my_main.o(.text.my_main)
    scan_key_pgr                             0x08003441   Thumb Code    20  scan_key.o(.text.scan_key_pgr)
    task_handler                             0x08003459   Thumb Code    40  system.o(.text.task_handler)
    __aeabi_uidiv                            0x08003485   Thumb Code    68  aeabi_sdivfast.o(.text_divfast)
    __aeabi_idiv                             0x080034c9   Thumb Code   434  aeabi_sdivfast.o(.text_divfast)
    AHBPrescTable                            0x0800367c   Data          64  system_stm32g0xx.o(.rodata.AHBPrescTable)
    Region$$Table$$Base                      0x080036bc   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080036dc   Number         0  anon$$obj.o(Region$$Table)
    uwTickFreq                               0x20000000   Data           1  stm32g0xx_hal.o(.data..L_MergedGlobals)
    uwTickPrio                               0x20000004   Data           4  stm32g0xx_hal.o(.data..L_MergedGlobals)
    long_pressed_time_check                  0x20000010   Data           2  scan_key.o(.data..L_MergedGlobals)
    ScanKey                                  0x20000014   Data          48  scan_key.o(.data..L_MergedGlobals)
    CUSTOM_HID_Desc                          0x20000044   Data          32  usbd_desc.o(.data.CUSTOM_HID_Desc)
    SystemCoreClock                          0x20000080   Data           4  system_stm32g0xx.o(.data.SystemCoreClock)
    USBD_CUSTOM_HID                          0x20000084   Data          56  usbd_customhid.o(.data.USBD_CUSTOM_HID)
    USBD_CUSTOM_HID_DeviceDesc               0x200000f4   Data          18  usbd_desc.o(.data.USBD_CUSTOM_HID_DeviceDesc)
    USBD_CustomHID_fops_FS                   0x20000114   Data          16  usbd_custom_hid_if.o(.data.USBD_CustomHID_fops_FS)
    USBD_LangIDDesc                          0x20000124   Data           4  usbd_desc.o(.data.USBD_LangIDDesc)
    USBD_StringSerial                        0x20000128   Data          26  usbd_desc.o(.data.USBD_StringSerial)
    __libspace_start                         0x20000148   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200001a8   Data           0  libspace.o(.bss)
    USBD_StrDesc                             0x200001b8   Data         512  usbd_desc.o(.bss.USBD_StrDesc)
    hUsbDeviceFS                             0x20000410   Data         732  usb_device.o(.bss.hUsbDeviceFS)
    hpcd_USB_DRD_FS                          0x200006ec   Data         736  usbd_conf.o(.bss.hpcd_USB_DRD_FS)
    hspi3                                    0x200009cc   Data         100  spi.o(.bss.hspi3)
    htim6                                    0x20000a30   Data          76  tim.o(.bss.htim6)
    uwTick                                   0x20000a7c   Data           4  stm32g0xx_hal.o(.bss.uwTick)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000bd

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00003828, Max: 0x00020000, ABSOLUTE, COMPRESSED[0x00003798])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000036dc, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000bc   Data   RO            3    RESET               startup_stm32g0b1xx.o
    0x080000bc   0x080000bc   0x00000008   Code   RO         1528  * !!!main             c_p.l(__main.o)
    0x080000c4   0x080000c4   0x00000054   Code   RO         1707    !!!scatter          c_p.l(__scatter.o)
    0x08000118   0x08000118   0x00000002   Code   RO         1708    !!handler_null      c_p.l(__scatter.o)
    0x0800011a   0x0800011a   0x00000002   PAD
    0x0800011c   0x0800011c   0x0000001c   Code   RO         1711    !!handler_zi        c_p.l(__scatter_zi.o)
    0x08000138   0x08000138   0x00000002   Code   RO         1566    .ARM.Collect$$libinit$$00000000  c_p.l(libinit.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         1580    .ARM.Collect$$libinit$$00000002  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         1582    .ARM.Collect$$libinit$$00000004  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         1584    .ARM.Collect$$libinit$$00000006  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         1587    .ARM.Collect$$libinit$$0000000C  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         1589    .ARM.Collect$$libinit$$0000000E  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         1591    .ARM.Collect$$libinit$$00000010  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         1594    .ARM.Collect$$libinit$$00000013  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         1596    .ARM.Collect$$libinit$$00000015  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         1598    .ARM.Collect$$libinit$$00000017  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         1600    .ARM.Collect$$libinit$$00000019  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         1602    .ARM.Collect$$libinit$$0000001B  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         1604    .ARM.Collect$$libinit$$0000001D  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         1606    .ARM.Collect$$libinit$$0000001F  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         1608    .ARM.Collect$$libinit$$00000021  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         1610    .ARM.Collect$$libinit$$00000023  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         1612    .ARM.Collect$$libinit$$00000025  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         1614    .ARM.Collect$$libinit$$00000027  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         1618    .ARM.Collect$$libinit$$0000002E  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         1620    .ARM.Collect$$libinit$$00000030  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         1622    .ARM.Collect$$libinit$$00000032  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         1624    .ARM.Collect$$libinit$$00000034  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000002   Code   RO         1625    .ARM.Collect$$libinit$$00000035  c_p.l(libinit2.o)
    0x0800013c   0x0800013c   0x00000002   Code   RO         1660    .ARM.Collect$$libshutdown$$00000000  c_p.l(libshutdown.o)
    0x0800013e   0x0800013e   0x00000000   Code   RO         1688    .ARM.Collect$$libshutdown$$00000002  c_p.l(libshutdown2.o)
    0x0800013e   0x0800013e   0x00000000   Code   RO         1690    .ARM.Collect$$libshutdown$$00000004  c_p.l(libshutdown2.o)
    0x0800013e   0x0800013e   0x00000000   Code   RO         1693    .ARM.Collect$$libshutdown$$00000007  c_p.l(libshutdown2.o)
    0x0800013e   0x0800013e   0x00000000   Code   RO         1696    .ARM.Collect$$libshutdown$$0000000A  c_p.l(libshutdown2.o)
    0x0800013e   0x0800013e   0x00000000   Code   RO         1698    .ARM.Collect$$libshutdown$$0000000C  c_p.l(libshutdown2.o)
    0x0800013e   0x0800013e   0x00000000   Code   RO         1701    .ARM.Collect$$libshutdown$$0000000F  c_p.l(libshutdown2.o)
    0x0800013e   0x0800013e   0x00000002   Code   RO         1702    .ARM.Collect$$libshutdown$$00000010  c_p.l(libshutdown2.o)
    0x08000140   0x08000140   0x00000000   Code   RO         1530    .ARM.Collect$$rtentry$$00000000  c_p.l(__rtentry.o)
    0x08000140   0x08000140   0x00000000   Code   RO         1536    .ARM.Collect$$rtentry$$00000002  c_p.l(__rtentry2.o)
    0x08000140   0x08000140   0x00000006   Code   RO         1548    .ARM.Collect$$rtentry$$00000004  c_p.l(__rtentry4.o)
    0x08000146   0x08000146   0x00000000   Code   RO         1538    .ARM.Collect$$rtentry$$00000009  c_p.l(__rtentry2.o)
    0x08000146   0x08000146   0x00000004   Code   RO         1539    .ARM.Collect$$rtentry$$0000000A  c_p.l(__rtentry2.o)
    0x0800014a   0x0800014a   0x00000000   Code   RO         1541    .ARM.Collect$$rtentry$$0000000C  c_p.l(__rtentry2.o)
    0x0800014a   0x0800014a   0x00000008   Code   RO         1542    .ARM.Collect$$rtentry$$0000000D  c_p.l(__rtentry2.o)
    0x08000152   0x08000152   0x00000002   Code   RO         1571    .ARM.Collect$$rtexit$$00000000  c_p.l(rtexit.o)
    0x08000154   0x08000154   0x00000000   Code   RO         1631    .ARM.Collect$$rtexit$$00000002  c_p.l(rtexit2.o)
    0x08000154   0x08000154   0x00000004   Code   RO         1632    .ARM.Collect$$rtexit$$00000003  c_p.l(rtexit2.o)
    0x08000158   0x08000158   0x00000006   Code   RO         1633    .ARM.Collect$$rtexit$$00000004  c_p.l(rtexit2.o)
    0x0800015e   0x0800015e   0x00000002   PAD
    0x08000160   0x08000160   0x00000038   Code   RO            4    .text               startup_stm32g0b1xx.o
    0x08000198   0x08000198   0x00000040   Code   RO         1516    .text               c_p.l(rt_memclr.o)
    0x080001d8   0x080001d8   0x00000006   Code   RO         1526    .text               c_p.l(heapauxi.o)
    0x080001de   0x080001de   0x0000003e   Code   RO         1552    .text               c_p.l(sys_stackheap_outer.o)
    0x0800021c   0x0800021c   0x00000010   Code   RO         1555    .text               c_p.l(exit.o)
    0x0800022c   0x0800022c   0x00000008   Code   RO         1567    .text               c_p.l(libspace.o)
    0x08000234   0x08000234   0x0000000c   Code   RO         1626    .text               c_p.l(sys_exit.o)
    0x08000240   0x08000240   0x00000002   Code   RO         1649    .text               c_p.l(use_no_semi.o)
    0x08000242   0x08000242   0x00000000   Code   RO         1651    .text               c_p.l(indicate_semi.o)
    0x08000242   0x08000242   0x00000056   Code   RO         1705    .text               c_p.l(__dczerorl2.o)
    0x08000298   0x08000298   0x00000004   Code   RO          199    .text.CUSTOM_HID_DeInit_FS  usbd_custom_hid_if.o
    0x0800029c   0x0800029c   0x00000004   Code   RO          197    .text.CUSTOM_HID_Init_FS  usbd_custom_hid_if.o
    0x080002a0   0x080002a0   0x00000018   Code   RO          201    .text.CUSTOM_HID_OutEvent_FS  usbd_custom_hid_if.o
    0x080002b8   0x080002b8   0x00000008   Code   RO           15    .text.Error_Handler  main.o
    0x080002c0   0x080002c0   0x00000198   Code   RO          522    .text.HAL_GPIO_Init  stm32g0xx_hal_gpio.o
    0x08000458   0x08000458   0x0000000a   Code   RO          526    .text.HAL_GPIO_ReadPin  stm32g0xx_hal_gpio.o
    0x08000462   0x08000462   0x00000010   Code   RO          530    .text.HAL_GPIO_TogglePin  stm32g0xx_hal_gpio.o
    0x08000472   0x08000472   0x00000010   Code   RO          528    .text.HAL_GPIO_WritePin  stm32g0xx_hal_gpio.o
    0x08000482   0x08000482   0x00000002   PAD
    0x08000484   0x08000484   0x00000008   Code   RO          760    .text.HAL_GetTick   stm32g0xx_hal.o
    0x0800048c   0x0800048c   0x00000068   Code   RO         1470    .text.HAL_IncTick   system.o
    0x080004f4   0x080004f4   0x0000002c   Code   RO          748    .text.HAL_Init      stm32g0xx_hal.o
    0x08000520   0x08000520   0x00000048   Code   RO          750    .text.HAL_InitTick  stm32g0xx_hal.o
    0x08000568   0x08000568   0x00000038   Code   RO           81    .text.HAL_MspInit   stm32g0xx_hal_msp.o
    0x080005a0   0x080005a0   0x00000038   Code   RO          706    .text.HAL_NVIC_SetPriority  stm32g0xx_hal_cortex.o
    0x080005d8   0x080005d8   0x00000024   Code   RO          296    .text.HAL_PCDEx_ActivateLPM  stm32g0xx_hal_pcd_ex.o
    0x080005fc   0x080005fc   0x00000040   Code   RO          286    .text.HAL_PCDEx_PMAConfig  stm32g0xx_hal_pcd_ex.o
    0x0800063c   0x0800063c   0x00000044   Code   RO          256    .text.HAL_PCD_EP_Close  stm32g0xx_hal_pcd.o
    0x08000680   0x08000680   0x00000050   Code   RO          254    .text.HAL_PCD_EP_Open  stm32g0xx_hal_pcd.o
    0x080006d0   0x080006d0   0x00000028   Code   RO          258    .text.HAL_PCD_EP_Receive  stm32g0xx_hal_pcd.o
    0x080006f8   0x080006f8   0x00000058   Code   RO          264    .text.HAL_PCD_EP_SetStall  stm32g0xx_hal_pcd.o
    0x08000750   0x08000750   0x0000002c   Code   RO          262    .text.HAL_PCD_EP_Transmit  stm32g0xx_hal_pcd.o
    0x0800077c   0x0800077c   0x00000244   Code   RO          212    .text.HAL_PCD_Init  stm32g0xx_hal_pcd.o
    0x080009c0   0x080009c0   0x00000078   Code   RO           89    .text.HAL_PCD_MspInit  usbd_conf.o
    0x08000a38   0x08000a38   0x0000002c   Code   RO          220    .text.HAL_PCD_Start  stm32g0xx_hal_pcd.o
    0x08000a64   0x08000a64   0x00000084   Code   RO          684    .text.HAL_PWREx_ControlVoltageScaling  stm32g0xx_hal_pwr_ex.o
    0x08000ae8   0x08000ae8   0x00000014   Code   RO          644    .text.HAL_PWREx_EnableVddUSB  stm32g0xx_hal_pwr_ex.o
    0x08000afc   0x08000afc   0x000002ac   Code   RO          422    .text.HAL_RCCEx_PeriphCLKConfig  stm32g0xx_hal_rcc_ex.o
    0x08000da8   0x08000da8   0x00000200   Code   RO          386    .text.HAL_RCC_ClockConfig  stm32g0xx_hal_rcc.o
    0x08000fa8   0x08000fa8   0x000004cc   Code   RO          384    .text.HAL_RCC_OscConfig  stm32g0xx_hal_rcc.o
    0x08001474   0x08001474   0x000000f8   Code   RO          854    .text.HAL_SPI_Init  stm32g0xx_hal_spi.o
    0x0800156c   0x0800156c   0x00000084   Code   RO           36    .text.HAL_SPI_MspInit  spi.o
    0x080015f0   0x080015f0   0x00000014   Code   RO          816    .text.HAL_SYSCFG_StrobeDBattpinsConfig  stm32g0xx_hal.o
    0x08001604   0x08001604   0x00000034   Code   RO          714    .text.HAL_SYSTICK_Config  stm32g0xx_hal_cortex.o
    0x08001638   0x08001638   0x00000074   Code   RO         1274    .text.HAL_TIMEx_MasterConfigSynchronization  stm32g0xx_hal_tim_ex.o
    0x080016ac   0x080016ac   0x0000004a   Code   RO          979    .text.HAL_TIM_Base_Init  stm32g0xx_hal_tim.o
    0x080016f6   0x080016f6   0x00000002   PAD
    0x080016f8   0x080016f8   0x00000028   Code   RO           51    .text.HAL_TIM_Base_MspInit  tim.o
    0x08001720   0x08001720   0x00000002   Code   RO           66    .text.HardFault_Handler  stm32g0xx_it.o
    0x08001722   0x08001722   0x00000002   PAD
    0x08001724   0x08001724   0x000000e8   Code   RO           26    .text.MX_GPIO_Init  gpio.o
    0x0800180c   0x0800180c   0x00000048   Code   RO           34    .text.MX_SPI3_Init  spi.o
    0x08001854   0x08001854   0x0000004c   Code   RO           49    .text.MX_TIM6_Init  tim.o
    0x080018a0   0x080018a0   0x00000054   Code   RO          161    .text.MX_USB_Device_Init  usb_device.o
    0x080018f4   0x080018f4   0x00000002   Code   RO           64    .text.NMI_Handler   stm32g0xx_it.o
    0x080018f6   0x080018f6   0x00000002   Code   RO           70    .text.PendSV_Handler  stm32g0xx_it.o
    0x080018f8   0x080018f8   0x00000002   Code   RO           68    .text.SVC_Handler   stm32g0xx_it.o
    0x080018fa   0x080018fa   0x00000008   Code   RO           72    .text.SysTick_Handler  stm32g0xx_it.o
    0x08001902   0x08001902   0x00000002   Code   RO         1310    .text.SystemInit    system_stm32g0xx.o
    0x08001904   0x08001904   0x000000f8   Code   RO          983    .text.TIM_Base_SetConfig  stm32g0xx_hal_tim.o
    0x080019fc   0x080019fc   0x00000028   Code   RO          180    .text.USBD_CUSTOM_HID_ConfigStrDescriptor  usbd_desc.o
    0x08001a24   0x08001a24   0x00000018   Code   RO         1432    .text.USBD_CUSTOM_HID_DataIn  usbd_customhid.o
    0x08001a3c   0x08001a3c   0x0000002c   Code   RO         1434    .text.USBD_CUSTOM_HID_DataOut  usbd_customhid.o
    0x08001a68   0x08001a68   0x00000058   Code   RO         1426    .text.USBD_CUSTOM_HID_DeInit  usbd_customhid.o
    0x08001ac0   0x08001ac0   0x0000000c   Code   RO          170    .text.USBD_CUSTOM_HID_DeviceDescriptor  usbd_desc.o
    0x08001acc   0x08001acc   0x00000038   Code   RO         1430    .text.USBD_CUSTOM_HID_EP0_RxReady  usbd_customhid.o
    0x08001b04   0x08001b04   0x0000000c   Code   RO         1442    .text.USBD_CUSTOM_HID_GetDeviceQualifierDesc  usbd_customhid.o
    0x08001b10   0x08001b10   0x00000040   Code   RO         1438    .text.USBD_CUSTOM_HID_GetFSCfgDesc  usbd_customhid.o
    0x08001b50   0x08001b50   0x00000040   Code   RO         1436    .text.USBD_CUSTOM_HID_GetHSCfgDesc  usbd_customhid.o
    0x08001b90   0x08001b90   0x00000044   Code   RO         1440    .text.USBD_CUSTOM_HID_GetOtherSpeedCfgDesc  usbd_customhid.o
    0x08001bd4   0x08001bd4   0x0000009e   Code   RO         1424    .text.USBD_CUSTOM_HID_Init  usbd_customhid.o
    0x08001c72   0x08001c72   0x00000002   PAD
    0x08001c74   0x08001c74   0x0000002c   Code   RO          182    .text.USBD_CUSTOM_HID_InterfaceStrDescriptor  usbd_desc.o
    0x08001ca0   0x08001ca0   0x0000000c   Code   RO          172    .text.USBD_CUSTOM_HID_LangIDStrDescriptor  usbd_desc.o
    0x08001cac   0x08001cac   0x0000001c   Code   RO          174    .text.USBD_CUSTOM_HID_ManufacturerStrDescriptor  usbd_desc.o
    0x08001cc8   0x08001cc8   0x00000038   Code   RO          176    .text.USBD_CUSTOM_HID_ProductStrDescriptor  usbd_desc.o
    0x08001d00   0x08001d00   0x00000026   Code   RO         1446    .text.USBD_CUSTOM_HID_ReceivePacket  usbd_customhid.o
    0x08001d26   0x08001d26   0x0000001c   Code   RO         1448    .text.USBD_CUSTOM_HID_RegisterInterface  usbd_customhid.o
    0x08001d42   0x08001d42   0x00000002   PAD
    0x08001d44   0x08001d44   0x00000110   Code   RO          178    .text.USBD_CUSTOM_HID_SerialStrDescriptor  usbd_desc.o
    0x08001e54   0x08001e54   0x0000012c   Code   RO         1428    .text.USBD_CUSTOM_HID_Setup  usbd_customhid.o
    0x08001f80   0x08001f80   0x00000014   Code   RO         1383    .text.USBD_CtlError  usbd_ctlreq.o
    0x08001f94   0x08001f94   0x00000024   Code   RO         1406    .text.USBD_CtlPrepareRx  usbd_ioreq.o
    0x08001fb8   0x08001fb8   0x0000001e   Code   RO         1402    .text.USBD_CtlSendData  usbd_ioreq.o
    0x08001fd6   0x08001fd6   0x0000007a   Code   RO         1369    .text.USBD_GetEpDesc  usbd_core.o
    0x08002050   0x08002050   0x000000ac   Code   RO         1391    .text.USBD_GetString  usbd_ctlreq.o
    0x080020fc   0x080020fc   0x00000030   Code   RO         1325    .text.USBD_Init     usbd_core.o
    0x0800212c   0x0800212c   0x00000020   Code   RO          125    .text.USBD_LL_CloseEP  usbd_conf.o
    0x0800214c   0x0800214c   0x0000007c   Code   RO          115    .text.USBD_LL_Init  usbd_conf.o
    0x080021c8   0x080021c8   0x00000028   Code   RO          123    .text.USBD_LL_OpenEP  usbd_conf.o
    0x080021f0   0x080021f0   0x00000020   Code   RO          139    .text.USBD_LL_PrepareReceive  usbd_conf.o
    0x08002210   0x08002210   0x00000020   Code   RO          129    .text.USBD_LL_StallEP  usbd_conf.o
    0x08002230   0x08002230   0x00000020   Code   RO          119    .text.USBD_LL_Start  usbd_conf.o
    0x08002250   0x08002250   0x00000024   Code   RO          137    .text.USBD_LL_Transmit  usbd_conf.o
    0x08002274   0x08002274   0x00000042   Code   RO         1329    .text.USBD_RegisterClass  usbd_core.o
    0x080022b6   0x080022b6   0x00000008   Code   RO         1331    .text.USBD_Start    usbd_core.o
    0x080022be   0x080022be   0x00000002   Code   RO          147    .text.USBD_static_free  usbd_conf.o
    0x080022c0   0x080022c0   0x00000008   Code   RO          145    .text.USBD_static_malloc  usbd_conf.o
    0x080022c8   0x080022c8   0x00000224   Code   RO          324    .text.USB_ActivateEndpoint  stm32g0xx_ll_usb.o
    0x080024ec   0x080024ec   0x000000b0   Code   RO          326    .text.USB_DeactivateEndpoint  stm32g0xx_ll_usb.o
    0x0800259c   0x0800259c   0x0000000e   Code   RO          342    .text.USB_DevConnect  stm32g0xx_ll_usb.o
    0x080025aa   0x080025aa   0x0000001a   Code   RO          318    .text.USB_DevInit   stm32g0xx_ll_usb.o
    0x080025c4   0x080025c4   0x00000010   Code   RO          314    .text.USB_DisableGlobalInt  stm32g0xx_ll_usb.o
    0x080025d4   0x080025d4   0x0000003c   Code   RO          332    .text.USB_EPSetStall  stm32g0xx_ll_usb.o
    0x08002610   0x08002610   0x00000c2c   Code   RO          328    .text.USB_EPStartXfer  stm32g0xx_ll_usb.o
    0x0800323c   0x0800323c   0x00000010   Code   RO          312    .text.USB_EnableGlobalInt  stm32g0xx_ll_usb.o
    0x0800324c   0x0800324c   0x00000002   Code   RO         1500    .text.after_scan_key  scan_key.o
    0x0800324e   0x0800324e   0x00000004   Code   RO         1498    .text.before_scan_key  scan_key.o
    0x08003252   0x08003252   0x00000002   PAD
    0x08003254   0x08003254   0x00000140   Code   RO         1502    .text.key_event_get  scan_key.o
    0x08003394   0x08003394   0x00000018   Code   RO         1494    .text.key_flash_event  scan_key.o
    0x080033ac   0x080033ac   0x00000008   Code   RO         1496    .text.key_mode_event  scan_key.o
    0x080033b4   0x080033b4   0x00000086   Code   RO           11    .text.main          main.o
    0x0800343a   0x0800343a   0x00000006   Code   RO         1462    .text.my_main       my_main.o
    0x08003440   0x08003440   0x00000018   Code   RO         1504    .text.scan_key_pgr  scan_key.o
    0x08003458   0x08003458   0x0000002c   Code   RO         1480    .text.task_handler  system.o
    0x08003484   0x08003484   0x000001f6   Code   RO         1519    .text_divfast       c_p.l(aeabi_sdivfast.o)
    0x0800367a   0x0800367a   0x00000002   PAD
    0x0800367c   0x0800367c   0x00000040   Data   RO         1315    .rodata.AHBPrescTable  system_stm32g0xx.o
    0x080036bc   0x080036bc   0x00000020   Data   RO         1704    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080036e0, Size: 0x00001080, Max: 0x00024000, ABSOLUTE, COMPRESSED[0x000000b8])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000008   Data   RW          819    .data..L_MergedGlobals  stm32g0xx_hal.o
    0x20000008   COMPRESSED   0x00000006   Data   RW         1482    .data..L_MergedGlobals  system.o
    0x2000000e   COMPRESSED   0x00000002   PAD
    0x20000010   COMPRESSED   0x00000034   Data   RW         1506    .data..L_MergedGlobals  scan_key.o
    0x20000044   COMPRESSED   0x00000020   Data   RW          184    .data.CUSTOM_HID_Desc  usbd_desc.o
    0x20000064   COMPRESSED   0x00000019   Data   RW          204    .data.CUSTOM_HID_ReportDesc_FS  usbd_custom_hid_if.o
    0x2000007d   COMPRESSED   0x00000003   PAD
    0x20000080   COMPRESSED   0x00000004   Data   RW         1314    .data.SystemCoreClock  system_stm32g0xx.o
    0x20000084   COMPRESSED   0x00000038   Data   RW         1450    .data.USBD_CUSTOM_HID  usbd_customhid.o
    0x200000bc   COMPRESSED   0x00000029   Data   RW         1452    .data.USBD_CUSTOM_HID_CfgDesc  usbd_customhid.o
    0x200000e5   COMPRESSED   0x00000003   PAD
    0x200000e8   COMPRESSED   0x00000009   Data   RW         1451    .data.USBD_CUSTOM_HID_Desc  usbd_customhid.o
    0x200000f1   COMPRESSED   0x00000003   PAD
    0x200000f4   COMPRESSED   0x00000012   Data   RW          185    .data.USBD_CUSTOM_HID_DeviceDesc  usbd_desc.o
    0x20000106   COMPRESSED   0x00000002   PAD
    0x20000108   COMPRESSED   0x0000000a   Data   RW         1453    .data.USBD_CUSTOM_HID_DeviceQualifierDesc  usbd_customhid.o
    0x20000112   COMPRESSED   0x00000002   PAD
    0x20000114   COMPRESSED   0x00000010   Data   RW          203    .data.USBD_CustomHID_fops_FS  usbd_custom_hid_if.o
    0x20000124   COMPRESSED   0x00000004   Data   RW          186    .data.USBD_LangIDDesc  usbd_desc.o
    0x20000128   COMPRESSED   0x0000001a   Data   RW          187    .data.USBD_StringSerial  usbd_desc.o
    0x20000142   COMPRESSED   0x00000006   PAD
    0x20000148        -       0x00000060   Zero   RW         1568    .bss                c_p.l(libspace.o)
    0x200001a8        -       0x0000000e   Zero   RW         1483    .bss..L_MergedGlobals.1  system.o
    0x200001b6   COMPRESSED   0x00000002   PAD
    0x200001b8        -       0x00000200   Zero   RW          188    .bss.USBD_StrDesc   usbd_desc.o
    0x200003b8        -       0x00000058   Zero   RW          152    .bss.USBD_static_malloc.mem  usbd_conf.o
    0x20000410        -       0x000002dc   Zero   RW          163    .bss.hUsbDeviceFS   usb_device.o
    0x200006ec        -       0x000002e0   Zero   RW          151    .bss.hpcd_USB_DRD_FS  usbd_conf.o
    0x200009cc        -       0x00000064   Zero   RW           40    .bss.hspi3          spi.o
    0x20000a30        -       0x0000004c   Zero   RW           55    .bss.htim6          tim.o
    0x20000a7c        -       0x00000004   Zero   RW          818    .bss.uwTick         stm32g0xx_hal.o
    0x20000a80        -       0x00000200   Zero   RW            2    HEAP                startup_stm32g0b1xx.o
    0x20000c80        -       0x00000400   Zero   RW            1    STACK               startup_stm32g0b1xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       232         20          0          0          0       2063   gpio.o
       142          0          0          0          0       3481   main.o
         6          0          0          0          0        358   my_main.o
       382         12          0         52          0       3558   scan_key.o
       204          8          0          0        100       5295   spi.o
        56         26        188          0       1536        620   startup_stm32g0b1xx.o
       144         16          0          8          4       8095   stm32g0xx_hal.o
       108         20          0          0          0       7052   stm32g0xx_hal_cortex.o
       450         12          0          0          0       4851   stm32g0xx_hal_gpio.o
        56          4          0          0          0       1285   stm32g0xx_hal_msp.o
       944          0          0          0          0      17641   stm32g0xx_hal_pcd.o
       100          0          0          0          0       5682   stm32g0xx_hal_pcd_ex.o
       152         16          0          0          0       8301   stm32g0xx_hal_pwr_ex.o
      1740         80          0          0          0       9829   stm32g0xx_hal_rcc.o
       684         20          0          0          0      10811   stm32g0xx_hal_rcc_ex.o
       248          4          0          0          0      27037   stm32g0xx_hal_spi.o
       322         44          0          0          0      67338   stm32g0xx_hal_tim.o
       116          0          0          0          0      27721   stm32g0xx_hal_tim_ex.o
        16          0          0          0          0        702   stm32g0xx_it.o
      3972        128          0          0          0      37305   stm32g0xx_ll_usb.o
       148         20          0          6         14       4189   system.o
         2          0         64          4          0       1982   system_stm32g0xx.o
       116         16          0          0         76       5523   tim.o
        84         16          0          0        732       3149   usb_device.o
       458         28          0          0        824      16481   usbd_conf.o
       244          0          0          0          0      11282   usbd_core.o
       192          0          0          0          0      11732   usbd_ctlreq.o
        32          4          0         41          0       1624   usbd_custom_hid_if.o
       944         34          0        116          0       9230   usbd_customhid.o
       464        104          0         80        512       4354   usbd_desc.o
        66          0          0          0          0       4745   usbd_ioreq.o

    ----------------------------------------------------------------------
     12836        <USER>        <GROUP>        324       3804     323316   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        12          0          0         17          6          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        86          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        86         10          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
       502          0          0          0          0         92   aeabi_sdivfast.o
        16          0          0          0          0         68   exit.o
         6          0          0          0          0        136   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        64          0          0          0          0        108   rt_memclr.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         60   sys_exit.o
        62          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o

    ----------------------------------------------------------------------
       924         <USER>          <GROUP>          0         96        748   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       918         18          0          0         96        748   c_p.l

    ----------------------------------------------------------------------
       924         <USER>          <GROUP>          0         96        748   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     13760        650        284        324       3900     323368   Grand Totals
     13760        650        284        184       3900     323368   ELF Image Totals (compressed)
     13760        650        284        184          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                14044 (  13.71kB)
    Total RW  Size (RW Data + ZI Data)              4224 (   4.12kB)
    Total ROM Size (Code + RO Data + RW Data)      14228 (  13.89kB)

==============================================================================

