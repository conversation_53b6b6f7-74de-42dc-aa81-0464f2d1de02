/**
  ******************************************************************************
  * @file    stm32g0xx_ll_utils.h
  * <AUTHOR> Application Team
  * @brief   Header file of UTILS LL module.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2018 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  @verbatim
  ==============================================================================
                     ##### How to use this driver #####
  ==============================================================================
    [..]
    The LL UTILS driver contains a set of generic APIs that can be
    used by user:
      (+) Device electronic signature
      (+) Timing functions
      (+) PLL configuration functions

  @endverbatim
  ******************************************************************************
  */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef STM32G0xx_LL_UTILS_H
#define STM32G0xx_LL_UTILS_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32g0xx.h"

/** @addtogroup STM32G0xx_LL_Driver
  * @{
  */

/** @defgroup UTILS_LL UTILS
  * @{
  */

/* Private types -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/* Private constants ---------------------------------------------------------*/
/** @defgroup UTILS_LL_Private_Constants UTILS Private Constants
  * @{
  */

/* Max delay can be used in LL_mDelay */
#define LL_MAX_DELAY                  0xFFFFFFFFU

/**
 * @brief Unique device ID register base address
 */
#define UID_BASE_ADDRESS              UID_BASE

/**
 * @brief Flash size data register base address
 */
#define FLASHSIZE_BASE_ADDRESS        FLASHSIZE_BASE

/**
 * @brief Package data register base address
 */
#define PACKAGE_BASE_ADDRESS          PACKAGE_BASE

/**
  * @}
  */

/* Private macros ------------------------------------------------------------*/
/** @defgroup UTILS_LL_Private_Macros UTILS Private Macros
  * @{
  */
/**
  * @}
  */
/* Exported types ------------------------------------------------------------*/
/** @defgroup UTILS_LL_ES_INIT UTILS Exported structures
  * @{
  */
/**
  * @brief  UTILS PLL structure definition
  */
typedef struct
{
  uint32_t PLLM;   /*!< Division factor for PLL VCO input clock.
                        This parameter can be a value of @ref RCC_LL_EC_PLLM_DIV

                        This feature can be modified afterwards using unitary function
                        @ref LL_RCC_PLL_ConfigDomain_SYS(). */

  uint32_t PLLN;   /*!< Multiplication factor for PLL VCO output clock.
                        This parameter must be a number between Min_Data = 8 and Max_Data = 86

                        This feature can be modified afterwards using unitary function
                        @ref LL_RCC_PLL_ConfigDomain_SYS(). */

  uint32_t PLLR;   /*!< Division for the main system clock.
                        This parameter can be a value of @ref RCC_LL_EC_PLLR_DIV

                        This feature can be modified afterwards using unitary function
                        @ref LL_RCC_PLL_ConfigDomain_SYS(). */
} LL_UTILS_PLLInitTypeDef;

/**
  * @brief  UTILS System, AHB and APB buses clock configuration structure definition
  */
typedef struct
{
  uint32_t AHBCLKDivider;         /*!< The AHB clock (HCLK) divider. This clock is derived from the system clock (SYSCLK).
                                       This parameter can be a value of @ref RCC_LL_EC_SYSCLK_DIV

                                       This feature can be modified afterwards using unitary function
                                       @ref LL_RCC_SetAHBPrescaler(). */

  uint32_t APB1CLKDivider;        /*!< The APB1 clock (PCLK1) divider. This clock is derived from the AHB clock (HCLK).
                                       This parameter can be a value of @ref RCC_LL_EC_APB1_DIV

                                       This feature can be modified afterwards using unitary function
                                       @ref LL_RCC_SetAPB1Prescaler(). */
} LL_UTILS_ClkInitTypeDef;

/**
  * @}
  */

/* Exported constants --------------------------------------------------------*/
/** @defgroup UTILS_LL_Exported_Constants UTILS Exported Constants
  * @{
  */

/** @defgroup UTILS_EC_HSE_BYPASS HSE Bypass activation
  * @{
  */
#define LL_UTILS_HSEBYPASS_OFF        0x00000000U       /*!< HSE Bypass is not enabled                */
#define LL_UTILS_HSEBYPASS_ON         0x00000001U       /*!< HSE Bypass is enabled                    */
/**
  * @}
  */

/** @defgroup UTILS_EC_PACKAGETYPE PACKAGE TYPE
  * @{
  */
#if defined(STM32G0C1xx) || defined(STM32G0B1xx) || defined(STM32G0B0xx)
#define LL_UTILS_PACKAGETYPE_QFP100         0x00000000U /*!< LQFP100  package type                               */
#define LL_UTILS_PACKAGETYPE_QFN32_GP       0x00000001U /*!< LQFP32/UFQFPN32 General purpose (GP)                */
#define LL_UTILS_PACKAGETYPE_QFN32_N        0x00000002U /*!< LQFP32/UFQFPN32 N-version                           */
#define LL_UTILS_PACKAGETYPE_QFN48_GP       0x00000004U /*!< LQFP48/UFQPN48 General purpose (GP)                 */
#define LL_UTILS_PACKAGETYPE_QFN48_N        0x00000005U /*!< LQFP48/UFQPN48 N-version                            */
#define LL_UTILS_PACKAGETYPE_WLCSP52        0x00000006U /*!< WLCSP52                                             */
#define LL_UTILS_PACKAGETYPE_QFN64_GP       0x00000007U /*!< LQFP64 General purpose (GP)                         */
#define LL_UTILS_PACKAGETYPE_QFN64_N        0x00000008U /*!< LQFP64 N-version                                    */
#define LL_UTILS_PACKAGETYPE_BGA64_N        0x0000000AU /*!< UFBGA64 N-version                                   */
#define LL_UTILS_PACKAGETYPE_QFP80          0x0000000BU /*!< LQFP80  package type                                */
#define LL_UTILS_PACKAGETYPE_BGA100         0x0000000CU /*!< UBGA100  package type                               */
#elif defined(STM32G061xx) || defined(STM32G051xx) || defined(STM32G050xx) || defined(STM32G041xx) || defined(STM32G031xx) || defined(STM32G030xx)
#define LL_UTILS_PACKAGETYPE_SO8            0x00000001U /*!< SO8 package type                                    */
#define LL_UTILS_PACKAGETYPE_WLCSP18        0x00000002U /*!< WLCSP18 package type                                */
#define LL_UTILS_PACKAGETYPE_TSSOP20        0x00000003U /*!< TSSOP20 package type                                */
#define LL_UTILS_PACKAGETYPE_QFP28          0x00000004U /*!< UFQFPN28 package type                               */
#define LL_UTILS_PACKAGETYPE_QFN32          0x00000005U /*!< UFQFPN32 / LQFP32 package type                      */
#define LL_UTILS_PACKAGETYPE_QFN48          0x00000007U /*!< UFQFPN48 / LQFP48 package type                      */
#elif defined(STM32G081xx) || defined(STM32G071xx) || defined(STM32G070xx)
#define LL_UTILS_PACKAGETYPE_QFN28_GP       0x00000000U /*!< UFQFPN28 general purpose (GP) package type          */
#define LL_UTILS_PACKAGETYPE_QFN28_PD       0x00000001U /*!< UFQFPN28 Power Delivery (PD)                        */
#define LL_UTILS_PACKAGETYPE_QFN32_GP       0x00000004U /*!< UFQFPN32 / LQFP32 general purpose (GP) package type */
#define LL_UTILS_PACKAGETYPE_QFN32_PD       0x00000005U /*!< UFQFPN32 / LQFP32 Power Delivery (PD) package type  */
#define LL_UTILS_PACKAGETYPE_QFN48          0x00000008U /*!< UFQFPN48 / LQFP488 package type                     */
#define LL_UTILS_PACKAGETYPE_QFP64          0x0000000CU /*!< LQPF64 package type                                 */
#endif /* STM32G0C1xx || STM32G0B1xx || STM32G0B0xx */
/**
  * @}
  */

/**
  * @}
  */

/* Exported macro ------------------------------------------------------------*/

/* Exported functions --------------------------------------------------------*/
/** @defgroup UTILS_LL_Exported_Functions UTILS Exported Functions
  * @{
  */

/** @defgroup UTILS_EF_DEVICE_ELECTRONIC_SIGNATURE DEVICE ELECTRONIC SIGNATURE
  * @{
  */

/**
  * @brief  Get Word0 of the unique device identifier (UID based on 96 bits)
  * @retval UID[31:0]: X and Y coordinates on the wafer expressed in BCD format
  */
__STATIC_INLINE uint32_t LL_GetUID_Word0(void)
{
  return (uint32_t)(READ_REG(*((uint32_t *)UID_BASE_ADDRESS)));
}

/**
  * @brief  Get Word1 of the unique device identifier (UID based on 96 bits)
  * @retval UID[63:32]: Wafer number (UID[39:32]) & LOT_NUM[23:0] (UID[63:40])
  */
__STATIC_INLINE uint32_t LL_GetUID_Word1(void)
{
  return (uint32_t)(READ_REG(*((uint32_t *)(UID_BASE_ADDRESS + 4U))));
}

/**
  * @brief  Get Word2 of the unique device identifier (UID based on 96 bits)
  * @retval UID[95:64]: Lot number (ASCII encoded) - LOT_NUM[55:24]
  */
__STATIC_INLINE uint32_t LL_GetUID_Word2(void)
{
  return (uint32_t)(READ_REG(*((uint32_t *)(UID_BASE_ADDRESS + 8U))));
}

/**
  * @brief  Get Flash memory size
  * @note   This bitfield indicates the size of the device Flash memory expressed in
  *         Kbytes. As an example, 0x040 corresponds to 64 Kbytes.
  * @retval FLASH_SIZE[15:0]: Flash memory size
  */
__STATIC_INLINE uint32_t LL_GetFlashSize(void)
{
  return (uint32_t)(READ_REG(*((uint32_t *)FLASHSIZE_BASE_ADDRESS)) & 0x0000FFFFUL);
}

/**
  * @brief  Get Package type
  * @retval PKG[3:0]: Package type - This parameter can be a value of @ref UTILS_EC_PACKAGETYPE
  * @if defined(STM32G0C1xx)
  *         @arg @ref LL_UTILS_PACKAGETYPE_QFP100
  *         @arg @ref LL_UTILS_PACKAGETYPE_QFN32_GP
  *         @arg @ref LL_UTILS_PACKAGETYPE_QFN32_N
  *         @arg @ref LL_UTILS_PACKAGETYPE_QFN48_GP
  *         @arg @ref LL_UTILS_PACKAGETYPE_QFN48_N
  *         @arg @ref LL_UTILS_PACKAGETYPE_WLCSP52
  *         @arg @ref LL_UTILS_PACKAGETYPE_QFN64_GP
  *         @arg @ref LL_UTILS_PACKAGETYPE_QFN64_N
  *         @arg @ref LL_UTILS_PACKAGETYPE_BGA64_N
  *         @arg @ref LL_UTILS_PACKAGETYPE_QFP80
  *         @arg @ref LL_UTILS_PACKAGETYPE_BGA100
  * @elif defined(STM32G061xx) || defined(STM32G041xx)
  *         @arg @ref LL_UTILS_PACKAGETYPE_SO8
  *         @arg @ref LL_UTILS_PACKAGETYPE_WLCSP18
  *         @arg @ref LL_UTILS_PACKAGETYPE_TSSOP20
  *         @arg @ref LL_UTILS_PACKAGETYPE_QFP28
  *         @arg @ref LL_UTILS_PACKAGETYPE_QFN32
  *         @arg @ref LL_UTILS_PACKAGETYPE_QFN48
  * @elif defined(STM32G081xx)
  *         @arg @ref LL_UTILS_PACKAGETYPE_QFN28_GP
  *         @arg @ref LL_UTILS_PACKAGETYPE_QFN28_PD
  *         @arg @ref LL_UTILS_PACKAGETYPE_QFN32_GP
  *         @arg @ref LL_UTILS_PACKAGETYPE_QFN32_PD
  *         @arg @ref LL_UTILS_PACKAGETYPE_QFN48
  *         @arg @ref LL_UTILS_PACKAGETYPE_QFP64
  * @endif
  *
  */
__STATIC_INLINE uint32_t LL_GetPackageType(void)
{
#if defined(STM32G0C1xx) || defined(STM32G0B1xx) || defined(STM32G0B0xx)
  return (uint32_t)(READ_REG(*((uint32_t *)PACKAGE_BASE_ADDRESS)) & 0x1FU);
#else
  return (uint32_t)(READ_REG(*((uint32_t *)PACKAGE_BASE_ADDRESS)) & 0xFU);
#endif /* STM32G0C1xx || STM32G0B1xx || STM32G0B0xx */
}

/**
  * @}
  */

/** @defgroup UTILS_LL_EF_DELAY DELAY
  * @{
  */

/**
  * @brief  This function configures the Cortex-M SysTick source of the time base.
  * @param  HCLKFrequency HCLK frequency in Hz (can be calculated thanks to RCC helper macro)
  * @note   When a RTOS is used, it is recommended to avoid changing the SysTick
  *         configuration by calling this function, for a delay use rather osDelay RTOS service.
  * @param  Ticks Frequency of Ticks (Hz)
  * @retval None
  */
__STATIC_INLINE void LL_InitTick(uint32_t HCLKFrequency, uint32_t Ticks)
{
  /* Configure the SysTick to have interrupt in 1ms time base */
  SysTick->LOAD  = (uint32_t)((HCLKFrequency / Ticks) - 1UL);  /* set reload register */
  SysTick->VAL   = 0UL;                                       /* Load the SysTick Counter Value */
  SysTick->CTRL  = SysTick_CTRL_CLKSOURCE_Msk |
                   SysTick_CTRL_ENABLE_Msk;                   /* Enable the Systick Timer */
}

void        LL_Init1msTick(uint32_t HCLKFrequency);
void        LL_mDelay(uint32_t Delay);

/**
  * @}
  */

/** @defgroup UTILS_EF_SYSTEM SYSTEM
  * @{
  */

void        LL_SetSystemCoreClock(uint32_t HCLKFrequency);
ErrorStatus LL_PLL_ConfigSystemClock_HSI(LL_UTILS_PLLInitTypeDef *UTILS_PLLInitStruct,
                                         LL_UTILS_ClkInitTypeDef *UTILS_ClkInitStruct);
ErrorStatus LL_PLL_ConfigSystemClock_HSE(uint32_t HSEFrequency, uint32_t HSEBypass,
                                         LL_UTILS_PLLInitTypeDef *UTILS_PLLInitStruct, LL_UTILS_ClkInitTypeDef *UTILS_ClkInitStruct);
ErrorStatus LL_SetFlashLatency(uint32_t HCLKFrequency);

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif /* STM32G0xx_LL_UTILS_H */
