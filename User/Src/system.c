//
// Created by <PERSON><PERSON> on 2025/8/15 15:07.
//

#include "system.h"
#include "tim.h"

#define TASK_NUM_MAX 3
#define TASK_INIT(reload, func) {0, (reload), (reload), (func)}

void task_schedule();

void HAL_IncTick(void) {
    task_schedule();
    uwTick += (uint32_t) uwTickFreq;
}

/**
 * @brief 微秒级延时（TIM6）
 * @param count 延时计数（us）
 */
void delay_us(const uint32_t count) {
    __HAL_TIM_SET_COUNTER(&htim6, 65535 - count);
    __HAL_TIM_CLEAR_FLAG(&htim6, TIM_FLAG_UPDATE);

    HAL_TIM_Base_Start(&htim6);
    // 避免CEN被意外关闭，导致定时器CNT不继续计数，添加CEN状态判断条件
    while (__HAL_TIM_GET_FLAG(&htim6, TIM_FLAG_UPDATE) == RESET && (TIM6->CR1 & TIM_CR1_CEN));
    HAL_TIM_Base_Stop(&htim6);
}

/**
 * @brief 毫秒级延时
 * @param count 延时计数（ms）
 */
void delay_ms(uint16_t count) {
    while (count--) {
        delay_us(1000);
    }
}

/**
 * @brief 定时翻转LED_RED
 */
void led_handle() {
    LED_RED_WriteT;
}

static TaskComps_t TaskComps[TASK_NUM_MAX] = {
    TASK_INIT(100, led_handle),
    TASK_INIT(0, NULL),         // 未使用的任务槽
    TASK_INIT(0, NULL),         // 未使用的任务槽
};

void task_schedule() {
    for (uint8_t i = 0; i < TASK_NUM_MAX; ++i) {
        if (TaskComps[i].tim_count) {
            TaskComps[i].tim_count--;
            if (TaskComps[i].tim_count == 0) {
                TaskComps[i].tim_count = TaskComps[i].tim_reload;
                TaskComps[i].run = true;
            }
        }
    }
}

void task_handler() {
    for (uint8_t i = 0; i < TASK_NUM_MAX; ++i) {
        if (TaskComps[i].run && TaskComps[i].p_task_func != NULL) {
            TaskComps[i].run = false;
            TaskComps[i].p_task_func();
        }
    }
}
