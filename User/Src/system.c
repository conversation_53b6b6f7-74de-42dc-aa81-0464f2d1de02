//
// Created by <PERSON><PERSON> on 2025/8/15 15:07.
//

#include "system.h"

#include "tim.h"


void delay_us(const uint32_t count) {
    __HAL_TIM_SET_COUNTER(&htim6, 65535 - count);
    __HAL_TIM_CLEAR_FLAG(&htim6, TIM_FLAG_UPDATE);

    HAL_TIM_Base_Start(&htim6);
    // 避免CEN被意外关闭，导致定时器CNT不继续计数，添加CEN状态判断条件
    while (__HAL_TIM_GET_FLAG(&htim6, TIM_FLAG_UPDATE) == RESET && (TIM6->CR1 & TIM_CR1_CEN));
    HAL_TIM_Base_Stop(&htim6);
}

void delay_ms(uint16_t count) {
    while (count--) {
        delay_us(1000);
    }
}
