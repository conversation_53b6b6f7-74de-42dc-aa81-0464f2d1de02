//
// Created by <PERSON><PERSON> on 2025/8/15 15:07.
//

#include "system.h"

#include "scan_key.h"
#include "tim.h"

#define MAX_TASK_NUM 3

// 任务初始化宏
#define TASK_INIT(func,time_reload) {0, (time_reload), (time_reload), (func)}

void task_schedule();

/**
 * @brief 重写HAL_IncTick函数
 */
void HAL_IncTick(void) {
    task_schedule();
    uwTick += (uint32_t) uwTickFreq;
}

/**
 * @brief 微秒级延时（TIM6）
 * @param count 延时计数（us）
 */
void delay_us(const uint32_t count) {
    __HAL_TIM_SET_COUNTER(&htim6, 65535 - count);
    __HAL_TIM_CLEAR_FLAG(&htim6, TIM_FLAG_UPDATE);

    HAL_TIM_Base_Start(&htim6);
    // 避免CEN被意外关闭，导致定时器CNT不继续计数，添加CEN状态判断条件
    while (__HAL_TIM_GET_FLAG(&htim6, TIM_FLAG_UPDATE) == RESET && (TIM6->CR1 & TIM_CR1_CEN));
    HAL_TIM_Base_Stop(&htim6);
}

/**
 * @brief 毫秒级延时
 * @param count 延时计数（ms）
 */
void delay_ms(uint16_t count) {
    while (count--) {
        delay_us(1000);
    }
}

/**
 * @brief 定时翻转LED_RED
 */
void led_handle() {
    // LED_RED_WriteT;
}

/**
 * @brief 任务列表
 */
static TaskComps_t TaskComps[MAX_TASK_NUM] = {
    TASK_INIT(scan_key_pgr, SCAN_KEY_TIME_SLICES),
    TASK_INIT(led_handle, 500),
};

/**
 * @brief 任务调度
 */
void task_schedule() {
    for (uint8_t i = 0; i < MAX_TASK_NUM; ++i) {
        if (TaskComps[i].tim_count) {
            TaskComps[i].tim_count--;
            if (TaskComps[i].tim_count == 0) {
                TaskComps[i].tim_count = TaskComps[i].tim_reload;
                TaskComps[i].run = true;
            }
        }
    }
}

/**
 * @brief 任务处理
 */
void task_handler() {
    for (uint8_t i = 0; i < MAX_TASK_NUM; ++i) {
        if (TaskComps[i].run) {
            TaskComps[i].run = false;
            TaskComps[i].p_task_func();
        }
    }
}
