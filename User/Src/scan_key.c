//
// Created by <PERSON><PERSON> on 2024/10/27.
//

#include "scan_key.h"
#include "system.h"

/**
 * @brief 读取按键io口
 * @param key_id 按键id
 * @return 按键状态
 */
bool key_gpio_read(const KeyEnum key_id) {
    switch (key_id) {
        case KEY_FLASH:
            return Key_Flash_Read;
        case KEY_MODE:
            return Key_Mode_Read;
        default:
            return false;
    }
}

/**
 * @brief Flash键事件
 * @param key_event 执行事件
 */
void key_flash_event(const KeyEventEnum key_event) {
    ScanKey.buttons[KEY_FLASH].key_event = key_event;
    switch (key_event) {
        case KEY_RELEASED:
            LED_RED_WriteT;
            break;
        case KEY_PRESSED:
            LED_RED_WriteT;
            break;
        case KEY_PRESSING:
            break;
        case KEY_LONG_PRESSED:
            break;
        case KEY_CLICKED:
            break;
        default:
            break;
    }
}

/**
 * @brief Mode键事件
 * @param key_event 执行事件
 */
void key_mode_event(const KeyEventEnum key_event) {
    ScanKey.buttons[KEY_MODE].key_event = key_event;
    switch (key_event) {
        case KEY_RELEASED:
            break;
        case KEY_PRESSED:
            break;
        case KEY_PRESSING:
            break;
        case KEY_LONG_PRESSED:
            break;
        case KEY_CLICKED:
            break;
        default:
            break;
    }
}

// 每次扫描前执行
bool before_scan_key() {
    return true;
}

// 每次扫描后执行
void after_scan_key() {
}

// 扫描按键初始化配置
ScanKeyStructType ScanKey = {
    {
        {KEY_FLASH, key_flash_event},
        {KEY_MODE, key_mode_event},
    },
    before_scan_key,
    after_scan_key,
};

uint16_t long_pressed_time_check = LONG_PRESSED_TIME_LIMIT;

// 按键扫描
void key_event_get() {
    for (int i = 0; i < KeyItemCount; ++i) {
        KeyStructType *Key = &ScanKey.buttons[i];
        // 按下
        if (!key_gpio_read(Key->key_id)) {
            Key->cur_press_time++;
            Key->released_time = 0;
            // 消抖，拉低超过20ms视为按下了
            if (Key->cur_press_time > PRESSED_TIME_LIMIT) {
                if (!Key->is_pressing) {
                    Key->key_event_prg(KEY_PRESSED);
                }
                Key->is_pressing = true;
                Key->key_event_prg(KEY_PRESSING);
                // 如果第一次触发则触发一次
                if (Key->cur_press_time > long_pressed_time_check && !Key->long_pr_sent) {
                    // 触发一次key_long_pressed
                    Key->key_event_prg(KEY_LONG_PRESSED);
                    Key->long_pr_sent = true;
                }
            }
        } else {
            // Key->last_press_time 没有值才赋值
            // 避免因为 Key->released_time 防抖导致 Key->last_press_time 清空
            if (Key->cur_press_time && !Key->last_press_time) {
                Key->last_press_time = Key->cur_press_time;
            }
            if (Key->released_time > PRESSED_TIME_LIMIT) {
                // 触发一次key_clicked
                if (Key->is_pressing) {
                    // 松手
                    // 若松手前按压时间不超过CLICKED_TIME_LIMIT才执行key_clicked
                    if (Key->last_press_time < CLICKED_TIME_LIMIT) {
                        Key->key_event_prg(KEY_CLICKED);
                    }
                    Key->key_event_prg(KEY_RELEASED);
                }
                Key->is_pressing = false;
                Key->last_press_time = 0;
                Key->long_pr_sent = false;
            }
            Key->released_time++;
            Key->cur_press_time = 0;
        }
    }
}

/**
 * @brief 按键扫描程序
 */
void scan_key_pgr() {
    if (!ScanKey.before_scan_pgr) {
        return;
    }
    key_event_get();
    ScanKey.after_scan_pgr();
}
