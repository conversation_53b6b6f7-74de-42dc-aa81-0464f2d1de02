#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
File.Version=6
GPIO.groupedBy=
KeepUserPlacement=false
Mcu.CPN=STM32G0B1RBT6
Mcu.Family=STM32G0
Mcu.IP0=NVIC
Mcu.IP1=RCC
Mcu.IP2=SPI3
Mcu.IP3=SYS
Mcu.IP4=TIM6
Mcu.IP5=USB_DEVICE
Mcu.IP6=USB_DRD_FS
Mcu.IPNb=7
Mcu.Name=STM32G0B1R(B-C-E)Tx
Mcu.Package=LQFP64
Mcu.Pin0=PC11
Mcu.Pin1=PC14-OSC32_IN (PC14)
Mcu.Pin10=PA10
Mcu.Pin11=PA11 [PA9]
Mcu.Pin12=PA12 [PA10]
Mcu.Pin13=PA13
Mcu.Pin14=PA14-BOOT0
Mcu.Pin15=PD2
Mcu.Pin16=PB5
Mcu.Pin17=PC10
Mcu.Pin18=VP_SYS_VS_Systick
Mcu.Pin19=VP_SYS_VS_DBSignals
Mcu.Pin2=PC15-OSC32_OUT (PC15)
Mcu.Pin20=VP_TIM6_VS_ClockSourceINT
Mcu.Pin21=VP_USB_DEVICE_VS_USB_DEVICE_CUSTOM_HID_FS
Mcu.Pin3=PF0-OSC_IN (PF0)
Mcu.Pin4=PF1-OSC_OUT (PF1)
Mcu.Pin5=PC0
Mcu.Pin6=PB13
Mcu.Pin7=PB14
Mcu.Pin8=PC6
Mcu.Pin9=PC7
Mcu.PinsNb=22
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32G0B1RBTx
MxCube.Version=6.15.0
MxDb.Version=DB.6.0.150
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SVC_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:true
NVIC.SysTick_IRQn=true\:3\:0\:false\:false\:true\:false\:true\:false
PA10.GPIOParameters=GPIO_Label
PA10.GPIO_Label=PA_TX_EN
PA10.Locked=true
PA10.Signal=GPIO_Output
PA11\ [PA9].Mode=Device
PA11\ [PA9].Signal=USB_DM
PA12\ [PA10].Mode=Device
PA12\ [PA10].Signal=USB_DP
PA13.Mode=Serial_Wire
PA13.Signal=SYS_SWDIO
PA14-BOOT0.Mode=Serial_Wire
PA14-BOOT0.Signal=SYS_SWCLK
PB13.GPIOParameters=GPIO_PuPd,GPIO_Label
PB13.GPIO_Label=KEY_MODE
PB13.GPIO_PuPd=GPIO_PULLUP
PB13.Locked=true
PB13.Signal=GPIO_Input
PB14.GPIOParameters=GPIO_PuPd,GPIO_Label
PB14.GPIO_Label=KEY_FLASH
PB14.GPIO_PuPd=GPIO_PULLUP
PB14.Locked=true
PB14.Signal=GPIO_Input
PB5.Locked=true
PB5.Mode=Full_Duplex_Master
PB5.Signal=SPI3_MOSI
PC0.GPIOParameters=GPIO_Label
PC0.GPIO_Label=POWER
PC0.Locked=true
PC0.Signal=GPIO_Output
PC10.Locked=true
PC10.Mode=Full_Duplex_Master
PC10.Signal=SPI3_SCK
PC11.Locked=true
PC11.Mode=Full_Duplex_Master
PC11.Signal=SPI3_MISO
PC14-OSC32_IN\ (PC14).GPIOParameters=GPIO_Label
PC14-OSC32_IN\ (PC14).GPIO_Label=LCD_BL_W
PC14-OSC32_IN\ (PC14).Locked=true
PC14-OSC32_IN\ (PC14).Signal=GPIO_Output
PC15-OSC32_OUT\ (PC15).GPIOParameters=GPIO_Label
PC15-OSC32_OUT\ (PC15).GPIO_Label=LCD_BL_O
PC15-OSC32_OUT\ (PC15).Locked=true
PC15-OSC32_OUT\ (PC15).Signal=GPIO_Output
PC6.GPIOParameters=GPIO_Label
PC6.GPIO_Label=PA_RX_EN
PC6.Locked=true
PC6.Signal=GPIO_Output
PC7.GPIOParameters=GPIO_Label
PC7.GPIO_Label=LED_RED
PC7.Locked=true
PC7.Signal=GPIO_Output
PD2.GPIOParameters=GPIO_Label
PD2.GPIO_Label=RF_SCS
PD2.Locked=true
PD2.Signal=GPIO_Output
PF0-OSC_IN\ (PF0).Mode=HSE-External-Oscillator
PF0-OSC_IN\ (PF0).Signal=RCC_OSC_IN
PF1-OSC_OUT\ (PF1).Mode=HSE-External-Oscillator
PF1-OSC_OUT\ (PF1).Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32G0B1RBTx
ProjectManager.FirmwarePackage=STM32Cube FW_G0 V1.6.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=STM32CubeIDE
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=QA_G0.ioc
ProjectManager.ProjectName=QA_G0
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_SPI3_Init-SPI3-false-HAL-true,4-MX_USB_Device_Init-USB_DEVICE-false-HAL-false,5-MX_TIM6_Init-TIM6-false-HAL-true
RCC.ADCFreq_Value=64000000
RCC.AHBFreq_Value=64000000
RCC.APBFreq_Value=64000000
RCC.APBTimFreq_Value=64000000
RCC.CECFreq_Value=32786.88524590164
RCC.CortexFreq_Value=64000000
RCC.EXTERNAL_CLOCK_VALUE=48000
RCC.FCLKCortexFreq_Value=64000000
RCC.FDCANFreq_Value=64000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=64000000
RCC.HSE_VALUE=8000000
RCC.HSI48_VALUE=48000000
RCC.HSI_VALUE=16000000
RCC.I2C1Freq_Value=64000000
RCC.I2C2Freq_Value=64000000
RCC.I2S1Freq_Value=64000000
RCC.I2S2Freq_Value=64000000
RCC.IPParameters=ADCFreq_Value,AHBFreq_Value,APBFreq_Value,APBTimFreq_Value,CECFreq_Value,CortexFreq_Value,EXTERNAL_CLOCK_VALUE,FCLKCortexFreq_Value,FDCANFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI48_VALUE,HSI_VALUE,I2C1Freq_Value,I2C2Freq_Value,I2S1Freq_Value,I2S2Freq_Value,LPTIM1Freq_Value,LPTIM2Freq_Value,LPUART1Freq_Value,LPUART2Freq_Value,LSCOPinFreq_Value,LSE_VALUE,LSI_VALUE,MCO1PinFreq_Value,MCO2PinFreq_Value,PLLN,PLLPoutputFreq_Value,PLLQoutputFreq_Value,PLLRCLKFreq_Value,PLLSourceVirtual,PWRFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,TIM15Freq_Value,TIM1Freq_Value,USART1Freq_Value,USART2Freq_Value,USART3Freq_Value,USBFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value
RCC.LPTIM1Freq_Value=64000000
RCC.LPTIM2Freq_Value=64000000
RCC.LPUART1Freq_Value=64000000
RCC.LPUART2Freq_Value=64000000
RCC.LSCOPinFreq_Value=32000
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO1PinFreq_Value=64000000
RCC.MCO2PinFreq_Value=64000000
RCC.PLLN=16
RCC.PLLPoutputFreq_Value=64000000
RCC.PLLQoutputFreq_Value=64000000
RCC.PLLRCLKFreq_Value=64000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.PWRFreq_Value=64000000
RCC.SYSCLKFreq_VALUE=64000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.TIM15Freq_Value=64000000
RCC.TIM1Freq_Value=64000000
RCC.USART1Freq_Value=64000000
RCC.USART2Freq_Value=64000000
RCC.USART3Freq_Value=64000000
RCC.USBFreq_Value=48000000
RCC.VCOInputFreq_Value=8000000
RCC.VCOOutputFreq_Value=128000000
SPI3.CalculateBaudRate=32.0 MBits/s
SPI3.Direction=SPI_DIRECTION_2LINES
SPI3.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate
SPI3.Mode=SPI_MODE_MASTER
SPI3.VirtualType=VM_MASTER
TIM6.IPParameters=Prescaler
TIM6.Prescaler=(64-1)
USB_DEVICE.CLASS_NAME_FS=CUSTOM_HID
USB_DEVICE.CUSTOM_HID_FS_BINTERVAL=0xA
USB_DEVICE.IPParameters=VirtualMode,VirtualModeFS,CLASS_NAME_FS,PRODUCT_STRING_CUSTOMHID_FS,MANUFACTURER_STRING,CUSTOM_HID_FS_BINTERVAL,USBD_CUSTOM_HID_REPORT_DESC_SIZE,USBD_CUSTOMHID_OUTREPORT_BUF_SIZE
USB_DEVICE.MANUFACTURER_STRING=Neewer
USB_DEVICE.PRODUCT_STRING_CUSTOMHID_FS=Neewer Android Flash Trigger
USB_DEVICE.USBD_CUSTOMHID_OUTREPORT_BUF_SIZE=64
USB_DEVICE.USBD_CUSTOM_HID_REPORT_DESC_SIZE=25
USB_DEVICE.VirtualMode=CustomHid
USB_DEVICE.VirtualModeFS=Custom_Hid_FS
VP_SYS_VS_DBSignals.Mode=DisableDeadBatterySignals
VP_SYS_VS_DBSignals.Signal=SYS_VS_DBSignals
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM6_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM6_VS_ClockSourceINT.Signal=TIM6_VS_ClockSourceINT
VP_USB_DEVICE_VS_USB_DEVICE_CUSTOM_HID_FS.Mode=CUSTOM_HID_FS
VP_USB_DEVICE_VS_USB_DEVICE_CUSTOM_HID_FS.Signal=USB_DEVICE_VS_USB_DEVICE_CUSTOM_HID_FS
board=custom
