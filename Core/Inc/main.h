/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32g0xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
#define LCD_BL_W_Pin GPIO_PIN_14
#define LCD_BL_W_GPIO_Port GPIOC
#define LCD_BL_O_Pin GPIO_PIN_15
#define LCD_BL_O_GPIO_Port GPIOC
#define POWER_Pin GPIO_PIN_0
#define POWER_GPIO_Port GPIOC
#define KEY_MODE_Pin GPIO_PIN_13
#define KEY_MODE_GPIO_Port GPIOB
#define KEY_FLASH_Pin GPIO_PIN_14
#define KEY_FLASH_GPIO_Port GPIOB
#define PA_RX_EN_Pin GPIO_PIN_6
#define PA_RX_EN_GPIO_Port GPIOC
#define LED_RED_Pin GPIO_PIN_7
#define LED_RED_GPIO_Port GPIOC
#define PA_TX_EN_Pin GPIO_PIN_10
#define PA_TX_EN_GPIO_Port GPIOA
#define RF_SCS_Pin GPIO_PIN_2
#define RF_SCS_GPIO_Port GPIOD

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
